#!/bin/bash

source .scripts/.variables/base_variables.sh

unset METEOR_WATCH_FORCE_POLLING

MONGO_PARAMS="socketTimeoutMS=0&connectTimeoutMS=0&serverSelectionTimeoutMS=0&maxIdleTimeMS=0"

export MONGO_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring&${MONGO_PARAMS}"
export MONGO_OPLOG_URL="mongodb://127.0.0.1:27017/local?replicaSet=edspring&${MONGO_PARAMS}"
export MONGO_URL_LOG="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring&${MONGO_PARAMS}"
export MONGO_DDP_QUEUE_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring&${MONGO_PARAMS}"
export EDSPRING_REPORTING_MONGO_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring&${MONG<PERSON>_PARAMS}"
export MONGO_IMPORT_URL="mongodb://127.0.0.1:27017/meteor?replicaSet=edspring&${MONGO_PARAMS}"

echo "export MONGO_URL=$MONGO_URL"
