import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext, useCallback } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";
import { useTracker } from "meteor/react-meteor-data";
import { chunk, flatten } from "lodash";
import { Button } from "react-bootstrap";

import { Loading } from "../../components/loading";
import Header from "../../components/admin-view/admin-header";
import GradeLevelScreeningInProgress from "../../components/admin-view/admin-screening-grade-level-progress";

import ClasswideQuickInfoSection from "../../components/admin-view/classwide-quick-info-section";
import IndividualQuickInfoSection from "../../components/admin-view/individual-quick-info-section";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Students } from "/imports/api/students/students";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import IndividualOverviewItem from "./individual-overview-item.jsx";
import {
  getInActiveSchoolYear,
  getMeteorUserSync,
  ninjalog,
  sortByGradeAndName
} from "/imports/api/utilities/utilities";
import ScrollIndicator from "../../components/scrollIndicator";
import ScrollIndicatorView from "../../components/scrollIndicatorView";
import { areSubscriptionsLoading, isHighSchoolGrade } from "../../utilities";
import { HighSchoolGroupsList } from "./high-school-groups-list";
import { Assessments } from "/imports/api/assessments/assessments";
import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { Rules } from "/imports/api/rules/rules";
import { compileResultsByStudentGroupsInGrade, compileScreeningStatusForGrade } from "./utilities";
import ClasswideInterventionTable from "./classwide-intervention-table";
import { getGrowthResults } from "/imports/api/assessmentGrowth/utilities";
import { GrowthChartWrapper } from "../student-groups/growth-chart-wrapper";
import ClasswideDetailModal from "../student-detail/classwide-detail-modal";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { SiteContext } from "/imports/contexts/SiteContext";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

const renderNoGroupsMessage = groupType => (
  <h3 className="stamped">{`No students in this grade are currently practicing ${groupType} interventions.`}</h3>
);

export const GradeOverviewComponent = props => {
  const [gradeDemographics] = useState(null);
  const [allClasswideStats, setAllClasswideStats] = useState(null);
  const [allIndividualStats, setAllIndividualStats] = useState(null);
  const [isDataFetching, setIsDataFetching] = useState(false);
  const [studentGroupEnrollments, setStudentGroupEnrollments] = useState([]);
  const [shouldShowDetailModal, setShouldShowDetailModal] = useState(false);
  const [growthStats, setGrowthStats] = useState(null);

  const updateClasswideStats = useCallback(() => {
    if (props.studentGroups) {
      setIsDataFetching(true);
      const studentGroupIds = props.studentGroups.map(sg => sg._id);
      Meteor.call(
        "calculateClasswideStatsForMultipleGroups",
        studentGroupIds,
        true,
        (err, { allClasswideResults = [], studentGroupEnrollments: enrollments }) => {
          if (err) {
            ninjalog.error({
              msg: `error getting classwide intervention stats: ${err}`,
              context: "admin"
            });
          } else {
            const allIndividualStatsData = [];
            allClasswideResults.forEach(group => {
              allIndividualStatsData.push({
                results: group.extendedIndividualResults.individualResults,
                interventionConsistency: group.extendedIndividualResults.interventionConsistency,
                groupId: group.studentGroupId
              });
            });
            setAllClasswideStats(allClasswideResults);
            setStudentGroupEnrollments(enrollments);
            setAllIndividualStats(allIndividualStatsData);
          }
          setIsDataFetching(false);
        }
      );
      if (props.grade === "HS") {
        Meteor.call(
          "Growth:getHSClasswideInterventionProgress",
          {
            orgid: props.orgid,
            studentGroupIds,
            siteId: props.studentGroups[0].siteId
          },
          (err, res) => {
            if (err) {
              Alert.error(err.message);
            } else {
              setGrowthStats(res);
            }
          }
        );
      }
    }
  }, [props.studentGroups, props.grade, props.orgid]);

  useEffect(() => {
    updateClasswideStats();
  }, [updateClasswideStats]);

  useEffect(() => {
    updateClasswideStats();
  }, [props.grade, props.siteName, updateClasswideStats]);

  const renderIndividualSummaryTable = () => {
    const individualAssessmentResults = props.assessmentResults.filter(ar => ar.type === "individual");
    const individualStudentRowContent = props.studentGroups.reduce((result, group, index) => {
      let curResult = result;
      if (!group.currentAssessmentResultIds) {
        return curResult;
      }
      const currentIndividualAssessmentResults = individualAssessmentResults.filter(ar =>
        group.currentAssessmentResultIds.includes(ar._id)
      );

      if (currentIndividualAssessmentResults.length) {
        if (!Array.isArray(curResult)) {
          curResult = [];
        }
        curResult.push(
          <IndividualOverviewItem
            key={index}
            ownerIds={group.ownerIds}
            group={group}
            ruleCountTotal={props.ruleSkillsCount}
            currentBMPeriod={props.currentBMPeriod}
            students={props.students}
            assessmentResults={individualAssessmentResults}
          />
        );
      }
      return curResult;
    }, renderNoGroupsMessage("individual"));
    if (Array.isArray(individualStudentRowContent)) {
      return (
        <div className="tblIntvSummaryTable">
          <div className="row rowIndvSummaryHeading">
            <div className="col-md-3">Teacher (Group)</div>
            <div className="col-md-2">Current Intervention</div>
            <div className="col-md-2">
              Most Recent <br /> Score Entry
            </div>
            <div className="col-md-2">
              Intervention <br /> Consistency
            </div>
            <div className="col-md-1">
              Average Weeks <br /> Per Skill
            </div>
            <div className="col-md-2 input-date">
              Calculations <br /> As Of Date
            </div>
          </div>
          {individualStudentRowContent}
        </div>
      );
    }
    return individualStudentRowContent;
  };

  const {
    growthChartResults,
    loading,
    siteId,
    headerTitle,
    grade,
    gradeLevelScreeningProgress,
    currentBMPeriod,
    siteName,
    studentGroups,
    inActiveSchoolYear,
    isHighSchoolGroup,
    assessmentResults,
    ruleSkillsCount,
    orgid
  } = props;

  if (loading) {
    return <Loading />;
  }
  return (
    <div>
      <Header keyLabel="gradeOverview" siteId={siteId} headerTitle={headerTitle} headerStats={gradeDemographics} />
      <ScrollIndicator
        container={null}
        targetSelector={".conOverviewMain"}
        indicatorComponent={<ScrollIndicatorView />}
        uniqKey={grade}
        wait={isDataFetching}
      >
        <div className="conOverviewMain animated fadeIn">
          {isHighSchoolGroup || !currentBMPeriod ? null : (
            <GradeLevelScreeningInProgress
              currentBMPeriod={currentBMPeriod}
              grade={grade}
              siteName={siteName}
              progressData={gradeLevelScreeningProgress}
              inActiveSchoolYear={inActiveSchoolYear}
              studentGroups={studentGroups}
              orgid={orgid}
              studentGroupEnrollments={studentGroupEnrollments}
            />
          )}
          {isDataFetching ? (
            <Loading />
          ) : (
            <div className="conOverviewContent">
              <HighSchoolGroupsList
                shouldDisplay={isHighSchoolGroup}
                studentGroups={studentGroups}
                siteId={siteId}
                grade={grade}
              />
              {inActiveSchoolYear ? (
                <ClasswideQuickInfoSection
                  data={allClasswideStats}
                  grade={grade}
                  siteId={siteId}
                  studentGroups={studentGroups}
                  orgid={orgid}
                />
              ) : null}
              <section>
                <div className="d-flex flex-row justify-content-between">
                  <h2>
                    <i className="fa fa-users" /> Classwide Interventions
                  </h2>
                  <span>
                    {shouldShowDetailModal ? (
                      <ClasswideDetailModal
                        onCloseModal={() => {
                          setShouldShowDetailModal(false);
                        }}
                        showModal={shouldShowDetailModal}
                        siteId={siteId}
                        grade={grade}
                        schoolYear={props.schoolYear}
                        componentContext="gradeDetail"
                      />
                    ) : (
                      <Button
                        className="btn btn-primary"
                        data-testid="gradeDetail"
                        onClick={() => {
                          setShouldShowDetailModal(true);
                        }}
                      >
                        Skill Progress by Class
                      </Button>
                    )}
                  </span>
                </div>
                <ClasswideInterventionTable
                  allClasswideStats={allClasswideStats}
                  studentGroups={studentGroups}
                  assessmentResults={assessmentResults}
                  studentGroupEnrollments={studentGroupEnrollments}
                  ruleSkillsCount={ruleSkillsCount}
                  currentBMPeriod={currentBMPeriod}
                  loading={!allClasswideStats}
                />
              </section>
              {inActiveSchoolYear ? <IndividualQuickInfoSection data={allIndividualStats} siteId={siteId} /> : null}
              <section>
                <h2>
                  <i className="fa fa-user" /> Individual Interventions
                </h2>
                {renderIndividualSummaryTable()}
              </section>
              <div className="studentDetailContent">
                {isHighSchoolGroup ? (
                  chunk(growthStats, 4).map((statChunk, index) => {
                    return (
                      <GrowthChartWrapper
                        comparisonPeriod={"all"}
                        growthChartResults={statChunk}
                        grade="HS"
                        key={`growthStats${index}`}
                        chartId={`growth_All_HS${index}`}
                        noChartTitleAndLegend={index > 0}
                      />
                    );
                  })
                ) : (
                  <>
                    <GrowthChartWrapper comparisonPeriod="fall" growthChartResults={growthChartResults} />
                    <GrowthChartWrapper comparisonPeriod="spring" growthChartResults={growthChartResults} />
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </ScrollIndicator>
    </div>
  );
};

GradeOverviewComponent.propTypes = {
  assessmentResults: PropTypes.array,
  bmPeriods: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  grade: PropTypes.string,
  gradeLevelScreeningProgress: PropTypes.array,
  headerTitle: PropTypes.string,
  loading: PropTypes.bool,
  percentMeetingTargetByClassAndSeason: PropTypes.array,
  ruleSkillsCount: PropTypes.number,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number,
  studentGroups: PropTypes.array,
  students: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool,
  isHighSchoolGroup: PropTypes.bool,
  growthChartResults: PropTypes.object
};

const GradeOverviewComponentWithTracker = ({
  grade,
  bmPeriods,
  currentBMPeriod,
  schoolYear: contextSchoolYear,
  orgid: contextOrgid,
  siteId: contextSiteId
}) => {
  const { schoolYear } = useContext(SchoolYearContext);
  const { orgId } = useContext(OrganizationContext);
  const { siteId: contextSiteIdFromContext, siteName: contextSiteName } = useContext(SiteContext);
  const { benchmarkPeriods } = useContext(StaticDataContext);

  const trackerData = useTracker(() => {
    let students;
    const curUser = getMeteorUserSync();
    const resolvedOrgid = contextOrgid || orgId || curUser?.profile?.orgid;
    const resolvedSiteId = contextSiteId || contextSiteIdFromContext || curUser?.profile?.siteAccess?.[0]?.siteId;
    const resolvedSchoolYear = contextSchoolYear || schoolYear;
    const headerTitle = getCurrentEnrolledGrade(grade);
    const studentGroups = StudentGroups.find({
      siteId: resolvedSiteId,
      grade,
      schoolYear: resolvedSchoolYear
    })
      .fetch()
      .sort(sortByGradeAndName);
    const studentGroupIds = studentGroups.map(sg => sg._id);
    const studentsSub = Meteor.subscribe("Students:PerGrade", {
      orgid: resolvedOrgid,
      grade,
      schoolYear: resolvedSchoolYear,
      siteId: resolvedSiteId
    });
    const growthSub = Meteor.subscribe("AssessmentGrowth", grade);
    const assessmentSub = Meteor.subscribe("AssessmentsForGrade", grade);
    const rulesSub = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroupIds[0] || "");
    const assessmentResultsSub = Meteor.subscribe(
      "AssessmentResults:IndividualByStudentGroupIds",
      studentGroupIds,
      resolvedSchoolYear,
      grade
    );
    const loading = areSubscriptionsLoading(studentsSub, growthSub, assessmentSub, rulesSub, assessmentResultsSub);
    if (!studentGroups || !studentGroups.length) {
      // No student groups found for this site and grade
      return { loading: true };
    }
    let growthChartResults = {};
    let assessmentResults = [];
    let percentMeetingTargetByClassAndSeason = [];
    let gradeLevelScreeningProgress = {
      grade,
      gradeGroups: []
    };

    if (!loading) {
      assessmentResults = AssessmentResults.find({
        studentGroupId: { $in: studentGroupIds }
      }).fetch();
      percentMeetingTargetByClassAndSeason = bmPeriods
        ? compileResultsByStudentGroupsInGrade(
            bmPeriods,
            grade,
            studentGroups,
            assessmentResults.filter(ar => ar.status === "COMPLETED" && ar.type === "benchmark")
          )
        : [];
      gradeLevelScreeningProgress = currentBMPeriod
        ? compileScreeningStatusForGrade(currentBMPeriod, studentGroups, grade, assessmentResults)
        : { grade, gradeGroups: [] };

      students = Students.find({ orgid: resolvedOrgid }).fetch();

      const gradeAssessments = Assessments.find({ associatedGrades: grade }, { fields: { name: 1 } }).fetch();
      const assessmentComparisonMap = AssessmentGrowth.findOne({ grade });
      if (!isHighSchoolGrade(grade)) {
        try {
          growthChartResults = getGrowthResults({
            history: flatten(studentGroups.map(sq => sq.history || [])),
            assessmentComparisonMap,
            assessments: gradeAssessments,
            bmPeriods: benchmarkPeriods
          });
        } catch (e) {
          const genericErrorMessage = "There was a problem while getting aggregate growth chart data";
          const growthChartErrorRegExp = new RegExp(/getGrowthResults/);
          Alert.error(growthChartErrorRegExp.test(e.message) ? e.message : genericErrorMessage, { timeout: 5000 });
        }
      }
    }

    // Calculate ruleSkillsCount
    let ruleSkillsCount = 0;
    if (!loading) {
      const gradeRule = Rules.findOne({ grade });
      ruleSkillsCount = gradeRule?.skills?.length || 0;
    }

    return {
      assessmentResults,
      gradeLevelScreeningProgress: gradeLevelScreeningProgress.gradeGroups,
      headerTitle,
      loading,
      orgid: resolvedOrgid,
      percentMeetingTargetByClassAndSeason,
      siteId: resolvedSiteId,
      siteName: contextSiteName || "",
      studentGroups,
      students,
      growthChartResults,
      isHighSchoolGroup: isHighSchoolGrade(grade),
      schoolYear: resolvedSchoolYear,
      ruleSkillsCount,
      bmPeriods,
      currentBMPeriod,
      curUser
    };
  }, [
    grade,
    bmPeriods,
    currentBMPeriod,
    contextSchoolYear,
    contextOrgid,
    contextSiteId,
    schoolYear,
    orgId,
    contextSiteIdFromContext,
    contextSiteName
  ]);

  // Handle async operations for inActiveSchoolYear
  const [inActiveSchoolYear, setInActiveSchoolYear] = useState(true);

  useEffect(() => {
    const fetchInActiveSchoolYear = async () => {
      if (trackerData.curUser && trackerData.schoolYear && trackerData.orgid) {
        try {
          const result = await getInActiveSchoolYear(trackerData.schoolYear, trackerData.curUser, trackerData.orgid);
          setInActiveSchoolYear(result);
        } catch (error) {
          console.error("Error fetching inActiveSchoolYear:", error);
          setInActiveSchoolYear(true); // Default to true on error
        }
      }
    };

    fetchInActiveSchoolYear();
  }, [trackerData.schoolYear, trackerData.curUser, trackerData.orgid]);

  return <GradeOverviewComponent {...trackerData} inActiveSchoolYear={inActiveSchoolYear} />;
};

GradeOverviewComponentWithTracker.propTypes = {
  grade: PropTypes.string,
  bmPeriods: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  schoolYear: PropTypes.number,
  orgid: PropTypes.string,
  siteId: PropTypes.string
};

export default withRouter(GradeOverviewComponentWithTracker);

export { GradeOverviewComponent as PureGradeOverview, GradeOverviewComponentWithTracker }; // for testing
