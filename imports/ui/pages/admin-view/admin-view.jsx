import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";

import { useTracker } from "meteor/react-meteor-data";
import BenchmarkPeriodHelpers from "/imports/api/benchmarkPeriods/methods";
import { areSubscriptionsLoading, isOnPrintPage } from "/imports/ui/utilities";

// Data
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";

// Components
import GradeOverview from "./grade-overview.jsx";
import SchoolOverview from "./school-overview.jsx";
import { AppDataContext } from "/imports/ui/routing/AppDataContext";
import NewsBanner from "/imports/ui/components/dashboard/news-banner";
import Loading from "/imports/ui/components/loading";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";
import { SiteContext } from "../../../contexts/SiteContext";

export const AdminOverview = ({
  loading,
  isPrinting,
  gradeId,
  bmPeriods,
  siteName,
  currentBMPeriod,
  orgid,
  siteId,
  screeningHidden,
  schoolYear
}) => {
  const [message, setMessage] = useState(null);
  const { schoolYear: contextSchoolYear, orgid: contextOrgid, siteId: contextSiteId } =
    useContext(AppDataContext) || {};

  const getActiveMessage = () => {
    Meteor.call("News:getActiveMessage", { siteId, orgid }, (err, result) => {
      if (!err) {
        setMessage(result);
      }
    });
  };

  useEffect(() => {
    getActiveMessage();
  }, [siteId, orgid]); // getActiveMessage is stable, no need to include in deps

  if (loading) {
    // TODO(fmazur) - remove message
    return (
      <div className="overviewContainer">
        <Loading message="admin view" />
      </div>
    );
  }

  const shouldDisplayNewsBanner = message;

  return (
    <div className={!isPrinting ? "overviewContainer" : null}>
      <div className={`news-admin-dashboard-offset ${shouldDisplayNewsBanner ? "withNewsBanner" : ""}`}>
        {shouldDisplayNewsBanner ? <NewsBanner message={message} /> : <div className="conNewsBannerPlaceholder" />}
      </div>
      {gradeId === "all" ? (
        <SchoolOverview
          bmPeriods={bmPeriods}
          siteName={siteName}
          currentBMPeriod={currentBMPeriod}
          orgid={orgid}
          siteId={siteId}
          isPrinting={isPrinting}
          screeningHidden={screeningHidden === "true"}
          schoolYear={schoolYear}
        />
      ) : (
        <GradeOverview
          grade={gradeId}
          bmPeriods={bmPeriods}
          siteName={siteName}
          currentBMPeriod={currentBMPeriod}
          schoolYear={contextSchoolYear || schoolYear}
          orgid={contextOrgid || orgid}
          siteId={contextSiteId || siteId}
        />
      )}
    </div>
  );
};

AdminOverview.propTypes = {
  gradeId: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  loading: PropTypes.bool,
  grades: PropTypes.array,
  studentGroups: PropTypes.array,
  bmPeriods: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  isPrinting: PropTypes.bool,
  screeningHidden: PropTypes.string,
  schoolYear: PropTypes.number
};

export const AdminOverviewWithTracker = ({ siteId, orgid, ...props }) => {
  const { schoolYear } = useContext(SchoolYearContext);
  const { site } = useContext(SiteContext);

  const trackerData = useTracker(() => {
    if (!schoolYear) {
      return { loading: true };
    }

    // Subs for parent and child components
    const isPrinting = isOnPrintPage();
    Meteor.subscribe("Users", { siteId, orgid });
    const bmpSub = Meteor.subscribe("BenchmarkPeriods");
    const studentGroupHandle = Meteor.subscribe("StudentGroups:PerSite", orgid, siteId, schoolYear);
    const arProjection = {
      studentGroupId: 1,
      type: 1,
      status: 1,
      benchmarkPeriodId: 1,
      grade: 1,
      classwideResults: 1,
      ruleResults: 1
    };
    const assResultsSub = Meteor.subscribe("AssessmentResults:ForSite", siteId, schoolYear, arProjection);
    const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, schoolYear);
    // const orgSub = Meteor.subscribe("Organizations", orgid);
    const loading = areSubscriptionsLoading(gradesSub, studentGroupHandle, assResultsSub, bmpSub);

    let bmPeriods;
    let currentBMPeriod;
    let siteName = "";

    if (!loading) {
      bmPeriods = BenchmarkPeriods.find().fetch();
      if (siteId) {
        if (site) {
          siteName = site.name;
        }
      }
      currentBMPeriod = BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
    }

    return {
      loading,
      bmPeriods,
      siteName,
      currentBMPeriod,
      orgid,
      siteId,
      isPrinting,
      schoolYear
    };
  }, [schoolYear, siteId, orgid]);

  return <AdminOverview {...props} {...trackerData} />;
};

AdminOverviewWithTracker.propTypes = {
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number
};

export { AdminOverview as PureAdminOverview };
export default AdminOverviewWithTracker;
