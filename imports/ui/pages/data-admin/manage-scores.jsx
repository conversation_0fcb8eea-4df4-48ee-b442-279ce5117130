import React, { useState, useEffect, useContext, useCallback } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { maxBy, keyBy, get, groupBy } from "lodash";
import Alert from "react-s-alert";
import moment from "moment";

import { Loading } from "/imports/ui/components/loading";

import ClearScreeningScoresModal from "./clear-screening-scores-modal";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import ClasswideInterventionProgress from "../../components/student-detail/classwide-intervention-progress";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { Students } from "/imports/api/students/students";
import { areSubscriptionsLoading } from "../../utilities";
import * as helpers from "../../components/student-groups/helperFunction";
import ManageScoresIndividual from "./manage-scores-individual";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext.jsx";
import { SiteContext } from "/imports/contexts/SiteContext.jsx";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

const ManageScores = ({
  benchmarkPeriodId,
  userPrivilegedRole,
  loading,
  completedAssessmentResults,
  orgid,
  schoolYear,
  screenings,
  studentGroup,
  studentGroupId,
  students,
  studentsInIntervention,
  studentsWithCompletedIntervention
}) => {
  const [state, setState] = useState({
    isClearScoresModalOpen: false,
    scroll: {
      lastReload: new Date().valueOf()
    },
    isEditing: false,
    fetchingData: false,
    groupStats: {},
    shouldShowIndividualInterventionGraph: false,
    selectedStudent: {}
  });

  // Local state to replace ClassContext functionality
  const [classContextState, setClassContextState] = useState({
    student: {},
    lastIndividualScoreUpdatedAt: 0,
    userPrivilegedRole,
    completedAssessmentResults
  });

  const getIndividualStats = useCallback(group => {
    setState(prev => ({ ...prev, fetchingData: true }));
    Meteor.call("CalculateIndividualStats", group, (err, res) => {
      if (!err) {
        return setState(prev => ({ ...prev, fetchingData: false, groupStats: res }));
      }
      return setState(prev => ({ ...prev, fetchingData: false }));
    });
  }, []);

  // Handle componentDidMount equivalent
  useEffect(() => {
    // eslint-disable-next-line no-unused-expressions
    studentGroup._id && getIndividualStats(studentGroup);
    setClassContextState(prev => ({
      ...prev,
      student: state.selectedStudent
    }));
    const completedAssessmentResultForStudent = completedAssessmentResults.filter(
      oar => oar.studentId === state.selectedStudent._id
    );
    setClassContextState(prev => ({
      ...prev,
      lastIndividualScoreUpdatedAt: get(completedAssessmentResultForStudent, "[0].lastScoreUpdatedAt") || 0
    }));
  }, []); // Only run on mount

  // Handle componentDidUpdate equivalent for studentGroup changes
  useEffect(() => {
    if (studentGroup._id) {
      getIndividualStats(studentGroup);
    }
  }, [studentGroup._id, getIndividualStats]);

  // Handle componentDidUpdate equivalent for selectedStudent changes
  useEffect(() => {
    if (state.selectedStudent._id) {
      setClassContextState(prev => ({
        ...prev,
        student: state.selectedStudent
      }));
      const completedAssessmentResultForStudent = completedAssessmentResults.filter(
        oar => oar.studentId === state.selectedStudent._id
      );
      setClassContextState(prev => ({
        ...prev,
        lastIndividualScoreUpdatedAt: get(completedAssessmentResultForStudent, "[0].lastScoreUpdatedAt") || 0
      }));
    }
  }, [state.selectedStudent._id, completedAssessmentResults]);

  // Handle componentDidUpdate equivalent for userPrivilegedRole changes
  useEffect(() => {
    setClassContextState(prev => ({
      ...prev,
      userPrivilegedRole
    }));
  }, [userPrivilegedRole]);

  // Handle componentDidUpdate equivalent for completedAssessmentResults changes
  useEffect(() => {
    setClassContextState(prev => ({
      ...prev,
      completedAssessmentResults
    }));
  }, [completedAssessmentResults]);

  const clearScreeningScores = useCallback(() => {
    Meteor.call("clearScreeningScores", { studentGroupId, benchmarkPeriodId, schoolYear }, error => {
      if (error) {
        console.log(`clearScreeningScores error: ${error}`);
        Alert.error(error.reason || "There was a problem while clearing screening scores", {
          timeout: 5000
        });
      } else {
        Alert.success("Screening scores have been cleared successfully", {
          timeout: 5000
        });
      }
    });
  }, [studentGroupId, benchmarkPeriodId, schoolYear]);

  const openClearScoresModal = useCallback(() => {
    setState(prev => ({ ...prev, isClearScoresModalOpen: true }));
  }, []);

  const closeClearScoresModal = useCallback(() => {
    setState(prev => ({ ...prev, isClearScoresModalOpen: false }));
  }, []);

  const toggleStudentIndividualInterventionGraph = useCallback((student = {}) => {
    setState(prevState => ({
      ...prevState,
      shouldShowIndividualInterventionGraph: !prevState.shouldShowIndividualInterventionGraph,
      selectedStudent: student
    }));
  }, []);

  const renderScreenings = useCallback(
    () =>
      screenings.map(screening => (
        <tr key={screening._id}>
          <td>{screening.name}</td>
          <td data-testid="screeningDate">{screening.date}</td>
          <td className="text-center" data-testid="screeningScoresEntered">
            {screening.scoresEntered || "0"}
          </td>
          <td className="text-center" data-testid="screeningScoresSkipped">
            {screening.scoresSkipped || "0"}
          </td>
          <td className="text-center">{screening.classwideInterventionsBegun || "No"}</td>
          <td>
            {screening.isCurrentBenchmarkWindow && (
              <button className="btn btn-outline-blue btn-xs" onClick={openClearScoresModal}>
                Clear Scores
              </button>
            )}
          </td>
        </tr>
      )),
    [screenings, openClearScoresModal]
  );

  const renderIndividualInterventionProgress = useCallback(() => {
    const studentsToDisplay = [
      ...helpers.sortStudentsByName(studentsInIntervention),
      ...helpers.sortStudentsByName(studentsWithCompletedIntervention)
    ];
    return studentsToDisplay.length ? (
      <table className="table table-condensed">
        <thead>
          <tr>
            <td className="small col-md-4 text-start">Student</td>
            <td className="small col-md-5 text-center">Current Intervention</td>
            <td className="small col-md-3 text-center">Last score entered</td>
          </tr>
        </thead>
        <tbody>
          {studentsToDisplay.map((s, i) => {
            const lastIndividualAssessmentResult = maxBy(
              completedAssessmentResults.filter(car => car.studentId === s._id),
              "lastScoreUpdatedAt"
            );
            const lastScoreUpdatedAt = get(lastIndividualAssessmentResult, "lastScoreUpdatedAt");
            const parsedLastScoreUpdatedAt = lastScoreUpdatedAt
              ? moment(lastScoreUpdatedAt).format("L")
              : "Start of interventions";

            return (
              <tr key={i}>
                <td data-testid={`${s._id}_row`}>
                  <div
                    className="clickable-student"
                    data-testid={`${s._id}_clickable`}
                    onClick={() => toggleStudentIndividualInterventionGraph(s)}
                  >
                    {helpers.getStudentName(s)}{" "}
                    <small className="student-display-id">{helpers.getStudentDispayId(s)}</small>
                  </div>
                </td>
                <td className="col-md-5 text-center" data-testid={`currentIntAssessmentRow_${i}`}>
                  {s.currentSkill ? (
                    get(s, "currentSkill.assessmentName", "Intervention Complete")
                  ) : (
                    <span className="text-danger">No current skill</span>
                  )}
                </td>
                <td className="col-md-3 text-center">{parsedLastScoreUpdatedAt}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    ) : (
      <div className="alert alert-info text-center">No individual intervention scores for this group found</div>
    );
  }, [
    studentsInIntervention,
    studentsWithCompletedIntervention,
    completedAssessmentResults,
    toggleStudentIndividualInterventionGraph
  ]);

  const shouldRenderClasswideInterventionProgress = useCallback(() => {
    return studentGroup.history && studentGroup.history.some(sgh => sgh.type === "classwide");
  }, [studentGroup.history]);

  const renderIndividualInterventionGraph = useCallback(() => {
    return (
      <React.Fragment>
        <button className="btn btn-primary" onClick={toggleStudentIndividualInterventionGraph}>
          Return to student list
        </button>
        {classContextState.student.history ? (
          <ManageScoresIndividual currentGrade={state.selectedStudent?.grade} orgid={orgid} />
        ) : (
          <h3 className="manage-scores-stamped">{`This student has started Individual Intervention, but doesn't have any scores yet`}</h3>
        )}
      </React.Fragment>
    );
  }, [toggleStudentIndividualInterventionGraph, classContextState.student.history, state.selectedStudent, orgid]);

  const refreshScroll = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      scroll: {
        lastReload: new Date().valueOf()
      }
    }));
  }, []);

  const renderClasswideProgress = useCallback(() => {
    return (
      <tr>
        <td colSpan="2">
          <div className="intervention-content classwide">
            <ClasswideInterventionProgress
              student={null}
              studentGroup={studentGroup}
              students={students}
              refreshScroll={refreshScroll}
              userPrivilegedRole={userPrivilegedRole}
            />
          </div>
        </td>
      </tr>
    );
  }, [studentGroup, students, refreshScroll, userPrivilegedRole]);

  if (loading || state.fetchingData) {
    return <Loading />;
  }

  return (
    <div>
      <div className="main-content">
        <div className="p-3">
          <h2 className="no-bottom-border">
            <span> Screenings</span>
          </h2>
          {screenings.length ? (
            <table className="table table-condensed">
              <thead>
                <tr>
                  <th className="col-md-2">Season</th>
                  <th className="col-md-2">Date</th>
                  <th className="small col-md-2 text-center">Scores Entered</th>
                  <th className="small col-md-2 text-center">Scores Skipped</th>
                  <th className="small col-md-3 text-center">Classwide Interventions Begun</th>
                </tr>
              </thead>
              <tbody>{renderScreenings()}</tbody>
            </table>
          ) : (
            <div className="alert alert-info text-center">No screenings for this group found</div>
          )}
          {userPrivilegedRole ? (
            <React.Fragment>
              <h2 className="no-bottom-border">
                <span> Individual Intervention Students</span>
              </h2>
              {state.shouldShowIndividualInterventionGraph
                ? renderIndividualInterventionGraph()
                : renderIndividualInterventionProgress()}
              <h2 className="no-bottom-border">
                <span> Classwide Interventions</span>
              </h2>
              {shouldRenderClasswideInterventionProgress() ? (
                <table className="table table-condensed">
                  <tbody>{renderClasswideProgress()}</tbody>
                </table>
              ) : (
                <div className="alert alert-info text-center">
                  No classwide intervention scores for this group found
                </div>
              )}
            </React.Fragment>
          ) : null}
        </div>
      </div>
      {state.isClearScoresModalOpen && (
        <ClearScreeningScoresModal onCloseModal={closeClearScoresModal} confirmAction={clearScreeningScores} />
      )}
    </div>
  );
};

ManageScores.propTypes = {
  benchmarkPeriodId: PropTypes.string,
  userPrivilegedRole: PropTypes.string,
  loading: PropTypes.bool,
  completedAssessmentResults: PropTypes.array,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number,
  screenings: PropTypes.array,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  students: PropTypes.array,
  studentsInIntervention: PropTypes.array,
  studentsWithCompletedIntervention: PropTypes.array
};

const getUserPrivilegedRole = orgid => {
  const user = getMeteorUserSync();
  const siteAccessObject = user?.profile.siteAccess.find(
    sa =>
      sa.role === "arbitraryIdsuperAdmin" ||
      sa.role === "arbitraryIduniversalDataAdmin" ||
      (sa.role === "arbitraryIddataAdmin" && user?.profile.orgid === orgid)
  );

  return siteAccessObject && siteAccessObject.role;
};

const ManageScoresWithTracker = ({ orgid, siteId, studentGroupId, schoolYear: propSchoolYear }) => {
  const { schoolYear, latestAvailableSchoolYear } = useContext(SchoolYearContext) || {};
  const { currentBenchmarkWindow } = useContext(SiteContext) || {};
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(schoolYear || propSchoolYear);

  useEffect(() => {
    if (schoolYear || propSchoolYear) {
      setResolvedSchoolYear(schoolYear || propSchoolYear);
    } else if (latestAvailableSchoolYear) {
      setResolvedSchoolYear(latestAvailableSchoolYear);
    }
  }, [orgid, schoolYear, propSchoolYear, latestAvailableSchoolYear]);

  const trackerData = useTracker(() => {
    if (!resolvedSchoolYear || !currentBenchmarkWindow) {
      return { loading: true };
    }

    const assessmentResultsHandler = Meteor.subscribe("AssessmentResultsForStudentGroup", studentGroupId, orgid);
    const benchmarkPeriodsHandler = Meteor.subscribe("BenchmarkPeriods");
    const benchmarkWindowsHandler = Meteor.subscribe("BenchmarkWindowsForSchool", siteId);
    const studentGroupEnrollmentsHandler = Meteor.subscribe("StudentGroupEnrollments:PerSite", { orgid, siteId });
    const studentGroupEnrollmentsSub = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroupId);
    const studentGroupsSub = Meteor.subscribe("StudentGroupsAssociatedWithUser", resolvedSchoolYear, siteId, orgid);
    const studentsSub = Meteor.subscribe("StudentsInStudentGroup", studentGroupId);

    const loading = areSubscriptionsLoading(
      assessmentResultsHandler,
      benchmarkPeriodsHandler,
      benchmarkWindowsHandler,
      studentGroupEnrollmentsHandler,
      studentGroupEnrollmentsSub,
      studentGroupsSub,
      studentsSub
    );

    const screenings = [];
    let benchmarkPeriodId = "";
    let userPrivilegedRole = "";
    let studentGroup = {};
    let students;
    let studentsInIntervention = [];
    let completedAssessmentResults = [];
    let studentsWithCompletedIntervention = [];

    if (!loading) {
      const benchmarkPeriods = BenchmarkPeriods.find({}, { fields: { name: 1 } }).fetch();
      const benchmarkPeriodMap = keyBy(benchmarkPeriods, "_id");

      benchmarkPeriodId = currentBenchmarkWindow?.benchmarkPeriodId;
      console.log("* LOG * currentBenchmarkWindow", currentBenchmarkWindow);

      const benchmarkScreenings = AssessmentResults.find(
        {
          type: "benchmark"
        },
        {
          fields: {
            scores: 1,
            benchmarkPeriodId: 1,
            schoolYear: 1,
            lastModified: 1,
            ruleResults: 1,
            status: 1,
            name: 1
          }
        }
      ).fetch();
      benchmarkScreenings.forEach(benchmarkScreening => {
        const scoresByStatus = groupBy(benchmarkScreening.scores, "status");
        const name = `${benchmarkPeriodMap[benchmarkScreening.benchmarkPeriodId].name} ${
          benchmarkScreening.schoolYear
        }`;
        const isScreeningInProgress = benchmarkScreening.status !== "COMPLETED";
        const date =
          !isScreeningInProgress && benchmarkScreening.lastModified
            ? benchmarkScreening.lastModified.date.toISOString().substr(0, 10)
            : "In progress";
        const classwideInterventionsBegun =
          benchmarkScreening.ruleResults && benchmarkScreening.ruleResults.passed === false ? "Yes" : "No";
        const isCurrentBenchmarkWindow =
          benchmarkScreening.schoolYear === currentBenchmarkWindow.schoolYear &&
          benchmarkScreening.benchmarkPeriodId === benchmarkPeriodId;
        screenings.push({
          _id: benchmarkScreening._id,
          name,
          benchmarkPeriodId: benchmarkScreening.benchmarkPeriodId,
          schoolYear: benchmarkScreening.schoolYear,
          date,
          scoresEntered: get(scoresByStatus, "COMPLETE.length", 0),
          scoresSkipped: get(scoresByStatus, "CANCELLED.length", 0),
          classwideInterventionsBegun,
          isCurrentBenchmarkWindow
        });
      });
      userPrivilegedRole = getUserPrivilegedRole(orgid);
      studentGroup = StudentGroups.findOne({ _id: studentGroupId });
      const studentGroupEnrollmentsByStudentId = keyBy(
        StudentGroupEnrollments.find({ studentGroupId }, { fields: { studentId: 1, isActive: 1 } }).fetch(),
        "studentId"
      );
      const studentIds = Object.keys(studentGroupEnrollmentsByStudentId);
      students = Students.find({ _id: { $in: studentIds } })
        .fetch()
        .map(s => ({ ...s, isActive: studentGroupEnrollmentsByStudentId[s._id].isActive }));

      const openAssessmentResults = AssessmentResults.find({
        studentGroupId,
        schoolYear: resolvedSchoolYear,
        type: "individual",
        status: "OPEN"
      }).fetch();
      studentsInIntervention = Students.find({ _id: { $in: openAssessmentResults.map(oar => oar.studentId) } })
        .fetch()
        .filter(s => s.history?.length);
      studentsWithCompletedIntervention = students.filter(s => s.currentSkill && !s.currentSkill.benchmarkAssessmentId);

      completedAssessmentResults = AssessmentResults.find(
        {
          studentGroupId,
          schoolYear: resolvedSchoolYear,
          status: "COMPLETED",
          type: "individual"
        },
        { fields: { studentId: 1, lastScoreUpdatedAt: 1, individualSkills: 1 }, sort: { lastScoreUpdatedAt: -1 } }
      ).fetch();
    }

    return {
      loading,
      siteId,
      screenings,
      benchmarkPeriodId,
      schoolYear: resolvedSchoolYear,
      userPrivilegedRole,
      studentGroup,
      students,
      studentsInIntervention,
      studentsWithCompletedIntervention,
      completedAssessmentResults
    };
  }, [orgid, siteId, studentGroupId, resolvedSchoolYear, currentBenchmarkWindow]);

  if (trackerData.loading) {
    return <Loading />;
  }

  return <ManageScores {...trackerData} orgid={orgid} siteId={siteId} studentGroupId={studentGroupId} />;
};

ManageScoresWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  studentGroupId: PropTypes.string,
  schoolYear: PropTypes.number
};

export default ManageScoresWithTracker;
