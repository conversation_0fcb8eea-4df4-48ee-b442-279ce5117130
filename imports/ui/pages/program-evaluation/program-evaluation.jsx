import React, { Component, useEffect, useState } from "react";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import { groupBy, intersection, range, uniq } from "lodash";

import * as utils from "/imports/api/utilities/utilities";
import * as reporting from "./reporting-data-methods";
import AdminScreeningResults from "/imports/ui/components/admin-view/admin-screening-results";
import BenchmarkPeriodHelpers from "/imports/api/benchmarkPeriods/methods";
import ClasswideInterventionTable from "../admin-view/classwide-intervention-table";
import Loading from "../../components/loading";
import ProgramEvaluationIndividualInterventionTable from "./program-evaluation-individual-intervention-table";
import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";
import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { AssessmentScoresUpload } from "/imports/api/assessmentScoresUpload/assessmentScoresUpload";
import { Assessments } from "/imports/api/assessments/assessments";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { Grades } from "/imports/api/grades/grades";
import { Rules } from "/imports/api/rules/rules";
import { Sites } from "/imports/api/sites/sites";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { normalizeGrade } from "/imports/api/utilities/sortingHelpers/normalizeSortItem";
import { areSubscriptionsLoading, isOnPrintPage, openPrintWindow } from "../../utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { getPercentage } from "./helpers";
import { Users } from "../../../api/users/users";
import { getNumberOfDigitsForSeries, getUserRolesByIdBySiteId } from "../../../api/districtReporting/helpers";
import BarGraph from "../district-reporting/bar-graph";
import DetailTable from "../student-detail/skillProgress";
import { getValuesByKey } from "/imports/api/utilities/utilities";
import { getSkillProgressByGrade, renderNoScoresBanner, renderSeasonToSeason } from "./reporting-data-methods";
import { getGrowthChartMarkup } from "../student-groups/growth-chart-wrapper";

class ProgramEvaluation extends Component {
  constructor(props) {
    super(props);

    this.state = {
      generatingPrintout: false,
      percentMeetingTargetByGradeAndSeason: []
    };
  }

  componentDidMount() {
    window.onmessage = event => {
      if (event.data === "printScheduled") {
        this.setState({ generatingPrintout: true });
      } else if (event.data === "printWindowClosed") {
        this.setState({ generatingPrintout: false });
        window.document.getElementById("print-iframe")?.remove();
        window.document.title = "SpringMath";
      }
    };
    this.setState({
      percentMeetingTargetByGradeAndSeason: reporting.calculatePercentageMeetingTargetByGradeAndSeason(this.props)
    });
  }

  componentDidUpdate(prevProps, prevState) {
    const { loading, schoolYear } = this.props;
    const { percentMeetingTargetByGradeAndSeason } = this.state;
    if (
      (!loading && loading !== prevProps.loading) ||
      prevState.percentMeetingTargetByGradeAndSeason.length !== percentMeetingTargetByGradeAndSeason.length ||
      schoolYear !== prevProps.schoolYear
    ) {
      this.setState({
        percentMeetingTargetByGradeAndSeason: reporting.calculatePercentageMeetingTargetByGradeAndSeason(this.props)
      });
    }
  }

  printPage = () => {
    const printURL = `/${this.props.orgid}/print/ProgramEvaluation?orgid=${this.props.orgid}&siteId=${this.props.siteId}&schoolYear=${this.props.schoolYear}`;
    openPrintWindow(printURL);
  };

  renderIntroduction = () => {
    const { siteName, schoolYear } = this.props;
    return (
      <div>
        <div className="text-center">
          <b>
            Program Evaluation for {siteName} ({`${schoolYear - 1}-${schoolYear % 100}`})
          </b>
        </div>
        <br />
        <p>
          This report is designed to help you review your SpringMath implementation for the selected year. We will
          provide you with data that will help you begin the process of determining the success of your implementation
          and the impact it had upon student results. Our goal is to provide you with the information you need to have
          discussions that lead to an enhanced use of SpringMath and accelerated student success.
        </p>
      </div>
    );
  };

  renderSection1 = () => {
    const {
      site,
      schoolYear,
      benchmarkPeriodsGroupId,
      bmPeriods,
      studentGroupsByGrade,
      studentGroupEnrollmentsByGroupId,
      assessmentResultsForSite
    } = this.props;
    const data = reporting.getScreeningByGradeData({
      schoolYear,
      benchmarkPeriodsGroupId,
      bmPeriods,
      studentGroupsByGrade,
      studentGroupEnrollmentsByGroupId,
      assessmentResultsForSite
    });
    const maxNumberOfDigits = getNumberOfDigitsForSeries(data);
    return (
      !site.isHighSchool && (
        <div>
          <p>
            <b>Implementation Questions</b>
          </p>
          <ul>
            <li>Was screening completed each season?</li>
          </ul>
          <div>
            <BarGraph
              chartName="Percent of Students Screened by Grade and Season"
              data={data}
              chartId={`programEvaluation_screenedByGrade`}
              options={reporting.getChartOptions({
                height: data[0].data.length * 55,
                shouldUseSecondaryLabel: true,
                subtitleText: "N = number of students enrolled when screening was completed",
                type: "column",
                nOffset: -35 - 5 * maxNumberOfDigits,
                categoryOffset: -40 - 5 * maxNumberOfDigits,
                categoryOffsetY: -15,
                animation: false
              })}
            />
          </div>
        </div>
      )
    );
  };

  renderSection2 = isPrinting => {
    const {
      gradesWithStudentGroupsInSite,
      assessmentResultsForSite,
      ruleAssessmentIdsByGrade,
      studentGroupsByGrade
    } = this.props;
    const data = reporting.getClasswideInterventionUseByGrade({
      gradesWithStudentGroupsInSite,
      assessmentResultsForSite,
      ruleAssessmentIdsByGrade,
      studentGroupsByGrade
    });
    const maxNumberOfDigits = getNumberOfDigitsForSeries(data);
    return (
      <div className="page-break-before">
        <p>
          <b>Classwide Interventions</b>
        </p>
        <p>
          We can use the average number of skills completed/mastered by classes to look at the amount of content
          learned, which is an indicator of intervention dosage.
        </p>
        <div>
          <ul>
            <li>Did all of the classes where classwide intervention was recommended begin the interventions?</li>
          </ul>
          <p>For classes that started classwide intervention:</p>
          <ul>
            <li>What percentage of the classwide intervention skills were mastered?</li>
            <li>What percentage of the classes made it all the way through the classwide skill sequence?</li>
          </ul>
        </div>
        <div>
          <BarGraph
            chartName="Classwide Intervention Use by Grade"
            data={data}
            chartId={`programEvaluation_proficientExternalMeasure`}
            options={reporting.getChartOptions({
              height: data[0].data.length * 55,
              categoryOffset: -40 - 5 * maxNumberOfDigits,
              categoryOffsetY: isPrinting ? -20 : -5,
              nOffset: -35 - 5 * maxNumberOfDigits,
              animation: false,
              customHeightOffset: isPrinting ? 160 : 120,
              shouldUseSecondaryLabel: true
            })}
          />
        </div>
      </div>
    );
  };

  renderClasswideInterventionUseByClass = isPrinting => {
    const {
      allClasswideStats,
      studentGroups,
      assessmentResultsForSite,
      currentBMPeriod,
      site,
      studentGroupEnrollmentsByGroupId
    } = this.props;
    const maxSmallStudentGroupSize = 10;
    const hasSmallStudentGroups = !!allClasswideStats?.find(
      stats => stats.numberOfStudentsInGroup <= maxSmallStudentGroupSize
    );
    const groupChunks = isPrinting ? [] : [studentGroups];
    const groupsWithCWI = studentGroups.filter(sg => sg.currentClasswideSkill);
    if (isPrinting) {
      const studentGroupsByGrade = groupBy(groupsWithCWI, "grade");
      Object.values(studentGroupsByGrade).forEach(studentGroupsInGrade => {
        let rowsPerPageLimit = 9;
        if (groupChunks.length === 1) {
          rowsPerPageLimit = hasSmallStudentGroups ? 5 : 7;
        }
        if (
          groupChunks[groupChunks.length - 1] === undefined ||
          groupChunks[groupChunks.length - 1]?.length + studentGroupsInGrade.length > rowsPerPageLimit
        ) {
          groupChunks.push([]);
        }
        if (studentGroupsInGrade.length > rowsPerPageLimit) {
          studentGroupsInGrade.forEach(group => {
            if (groupChunks.length === 1) {
              rowsPerPageLimit = hasSmallStudentGroups ? 5 : 7;
            } else {
              rowsPerPageLimit = 9;
            }
            if (groupChunks[groupChunks.length - 1]?.length >= rowsPerPageLimit) {
              groupChunks.push([]);
            }
            groupChunks[groupChunks.length - 1].push(group);
          });
        } else {
          groupChunks[groupChunks.length - 1].push(...studentGroupsInGrade);
        }
      });
    }
    const studentGroupEnrollments = Object.values(studentGroupEnrollmentsByGroupId).flat(1);
    return (
      <div className="page-break-before">
        {hasSmallStudentGroups ? (
          <p>
            Heterogeneous class groupings produce stronger growth, especially for lower-performing students. When the
            most vulnerable students are grouped together, it may not be appropriate to use a universal classwide
            fluency-building intervention. These students will benefit more from individualized intervention. When
            several children in a class are receiving individual intervention, SpringMath will recommend small groupings
            each week to make the process more efficient for the teacher.
          </p>
        ) : null}
        <ul>
          {!site.isHighSchool && <li>Were all students in each of your classes screened?</li>}
          <li>
            Were teachers completing the interventions and entering scores every week (see Intervention Consistency)?
          </li>
          <li>Were students making progress every week (see % of Scores Increasing)?</li>
        </ul>

        {groupChunks.map((studentGroupsInChunk, index) => {
          return (
            <div
              key={`cwi_use_by_class_${index}`}
              className={groupChunks.length - 1 === index ? "" : "page-break-after"}
            >
              <p className="text-center">
                <b>
                  Classwide Intervention Use by Class
                  {groupChunks.length > 1 && isPrinting ? ` ${index + 1}/${groupChunks.length}` : ""}
                </b>
              </p>
              <div className="program-evaluation-table-container">
                <ClasswideInterventionTable
                  allClasswideStats={allClasswideStats}
                  studentGroups={studentGroupsInChunk}
                  assessmentResults={assessmentResultsForSite}
                  studentGroupEnrollments={studentGroupEnrollments}
                  currentBMPeriod={currentBMPeriod}
                  loading={!allClasswideStats}
                  isSchoolOverview
                  isHighSchool={site.isHighSchool}
                  type="evaluation"
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  renderStudentClasswideSkillProgress = isPrinting => {
    const {
      gradesWithStudentGroupsInSite,
      assessmentResultsForSite,
      assessments,
      ruleAssessmentIdsByGrade,
      studentGroupsByGrade
    } = this.props;
    const assessmentNameById = getValuesByKey(assessments, "_id", "name");
    const classwideRules = Object.entries(ruleAssessmentIdsByGrade).reduce((a, [key, values]) => {
      a.push({ skills: values, grade: key });
      return a;
    }, []);

    const skillProgressByGrade = getSkillProgressByGrade({
      schoolsInOrg: [
        {
          completedAssessmentResults: assessmentResultsForSite.filter(a => a.status === "COMPLETED"),
          gradesWithGroups: gradesWithStudentGroupsInSite,
          studentGroupsByGrade
        }
      ],
      assessmentNameById,
      classwideRules
    });

    const printingFontSizeClassName = isPrinting ? "font-13" : "";
    return (
      <div className="page-break-before">
        {Object.values(skillProgressByGrade).flat(1).length ? (
          Object.entries(skillProgressByGrade).map(([grade, rowData], index) => {
            const shouldRenderTable = rowData?.find(rd => rd.columns.filter(Boolean)?.length);
            return (
              <div key={grade} className={index !== 0 ? "page-break-before" : ""}>
                <div>
                  <div className="d-flex flex-row justify-content-between">
                    <h5 className={printingFontSizeClassName}>Grade {grade}</h5>
                    <h5 className={printingFontSizeClassName}>
                      Percent of Students in Each Category when Mastery was Reached in Classwide Intervention
                    </h5>
                  </div>
                  {shouldRenderTable ? (
                    <p className={printingFontSizeClassName}>
                      Small classes with alternative mastery criteria are included in these calculations
                    </p>
                  ) : null}
                </div>
                {rowData.length && shouldRenderTable ? (
                  <DetailTable key={`skillProgress${index}`} rowData={rowData} componentContext={"compactTable"} />
                ) : (
                  renderNoScoresBanner({
                    message: "Classwide Intervention Scores not yet submitted for this student group in this grade",
                    isPrinting
                  })
                )}
                {!isPrinting ? <br /> : null}
              </div>
            );
          })
        ) : (
          <div>
            <h5 className={printingFontSizeClassName}>
              Percent of Students in Each Category when Mastery was Reached in Classwide Intervention
            </h5>
            {renderNoScoresBanner({
              message: "Classwide Intervention Scores not yet submitted for any student group in any grade",
              isPrinting
            })}
          </div>
        )}
      </div>
    );
  };

  renderSection3 = isPrinting => {
    return (
      <React.Fragment>
        {this.renderClasswideInterventionUseByClass(isPrinting)}
        {this.renderStudentClasswideSkillProgress(isPrinting)}
      </React.Fragment>
    );
  };

  renderSection4 = () => {
    const {
      studentsByGroupId,
      users,
      assessmentResultsForSite,
      gradesWithStudentGroupsInSite,
      studentGroupsByGrade,
      schoolYear
    } = this.props;
    const individualInterventionTableData = reporting.getIndividualInterventionsMarkup({
      assessmentResultsForSite,
      gradesWithStudentGroupsInSite,
      studentGroupsByGrade,
      studentsByGroupId,
      userRolesById: getUserRolesByIdBySiteId(users, schoolYear)
    });
    const shouldDisplayIndividualInterventionMessage = !!individualInterventionTableData.find(
      i => getPercentage(i.numGetting, i.numNeeding) < 50
    );
    return individualInterventionTableData.length ? (
      <div className="page-break-before">
        <p>
          <b>Individual Interventions</b>
        </p>
        <p>
          {shouldDisplayIndividualInterventionMessage
            ? "Individual intervention would be a good goal for enhanced implementation next year. "
            : null}
          Finished or completed individual interventions means students mastered all the necessary skills to no longer
          be at risk.
        </p>
        <ul>
          <li>
            When individual interventions were recommended were they implemented (see Number of students
            Recommended/Started/Completed individual interventions)?
          </li>
          <li>
            When individual interventions were started did the students make progress (see % of Scores Increasing)?
          </li>
          <li>
            Did students in individual intervention learn all the skills they needed (see Number of students
            Recommended/Started/Completed individual interventions)?
          </li>
        </ul>
        <div className="text-center">Individual Intervention use by Grade</div>
        <div>
          <ProgramEvaluationIndividualInterventionTable rows={individualInterventionTableData} />
        </div>
      </div>
    ) : null;
  };

  renderSeasonToSeasonGrowth = isPrinting => {
    const shouldDisplaySeasonToSeason =
      intersection(
        Object.keys(this.props.growthDataBySchoolYear || {}).map(s => parseInt(s)),
        [this.props.schoolYear - 1, this.props.schoolYear]
      ).length === 2;

    const fallToFallDataByGrade = {};
    const winterToWinterDataByGrade = {};
    /*
        {
          2024: {
            01: {
              winterToSpring: { fall: {}, winter...}
            }
          },
          2023: ...
        }
     */
    const customColorsByMeasurePeriod = {
      fall: "#ed8b00",
      winter: "#003349",
      spring: "#B6C1CB",
      classwide: "#565294"
    };

    Object.entries(this.props.growthDataBySchoolYear).forEach(([schoolYear, growthByGrade]) => {
      Object.entries(growthByGrade).forEach(([grade, growthSet]) => {
        fallToFallDataByGrade[grade] = fallToFallDataByGrade[grade] ?? [];
        winterToWinterDataByGrade[grade] = winterToWinterDataByGrade[grade] ?? [];
        const fallData = { categories: [], scores: [], schoolYear };
        const winterData = { categories: [], scores: [], schoolYear };
        const { chartItems } = getGrowthChartMarkup("all", growthSet, true, grade, customColorsByMeasurePeriod);
        for (let i = 0; i < chartItems.categories.length; i += 1) {
          if (chartItems.categories[i]?.measurePeriod === "fall") {
            fallData.categories.push(chartItems.categories[i]);
            fallData.scores.push(chartItems.scores[i]);
          }
          if (chartItems.categories[i]?.measurePeriod === "winter") {
            winterData.categories.push(chartItems.categories[i]);
            winterData.scores.push(chartItems.scores[i]);
          }
        }
        fallToFallDataByGrade[grade].push(fallData);
        winterToWinterDataByGrade[grade].push(winterData);
      });
    });

    return shouldDisplaySeasonToSeason ? (
      <React.Fragment>
        <h5 className="page-break-before">Fall to Fall growth by year and grade</h5>
        {renderSeasonToSeason({
          seasonData: fallToFallDataByGrade,
          graphContext: "Fall to Fall",
          schoolYear: this.props.schoolYear,
          isPrinting
        })}
        <h5 className="page-break-before">Winter to Winter growth by year and grade</h5>
        {renderSeasonToSeason({
          seasonData: winterToWinterDataByGrade,
          graphContext: "Winter to Winter",
          schoolYear: this.props.schoolYear,
          isPrinting
        })}
      </React.Fragment>
    ) : null;
  };

  renderSection5 = isPrinting => {
    const { site, gradesWithStudentGroupsInSite, studentGroupsByGrade, assessmentGrowths, assessments } = this.props;
    const { percentMeetingTargetByGradeAndSeason } = this.state;
    return (
      !site.isHighSchool && (
        <div className="page-break-before">
          <p>
            <b>Growth on SpringMath measures</b>
          </p>
          <p>
            For each grade, did you make progress on the screening measures (note that the difficulty of the screening
            measures increases each season, but also note that the skills reflect skills that position students for math
            success)?
          </p>
          {reporting.renderGrowthByGradeGraphs({
            gradesWithStudentGroupsInSite,
            studentGroupsByGrade,
            assessmentGrowths,
            assessments,
            isPrinting
          })}
          {this.renderSeasonToSeasonGrowth(isPrinting)}
          <p className="page-break-before">
            <b>Screening Results (Percent of Students Meeting Target)</b>
          </p>
          <div>
            <AdminScreeningResults
              targetScoreData={percentMeetingTargetByGradeAndSeason}
              grade="ALL"
              type="evaluation"
            />
          </div>
        </div>
      )
    );
  };

  getGradesStartingWithLowestContainingScores = (availableGradesInSite, assessmentScoresUpload) => {
    const onlyGrades = availableGradesInSite.map(g => g._id);
    const gradesWithScores = assessmentScoresUpload.map(obj => obj.grade);
    const minGrade = Math.min(...gradesWithScores.map(grade => onlyGrades.indexOf(grade)));
    return availableGradesInSite.slice(minGrade);
  };

  renderSection6 = isPrinting => {
    const {
      schoolYear,
      assessmentResultsForSite,
      assessmentScoresUpload,
      availableGrades,
      studentGroups,
      allClasswideStats,
      studentGroupEnrollmentsByGroupId,
      gradesWithStudentGroupsInSite
    } = this.props;
    const currentAndTwoPriorSchoolYears = range(schoolYear - 2, schoolYear + 1);
    const externalScoresSchoolYears = uniq(assessmentScoresUpload.map(asu => asu.schoolYear))
      .sort()
      .filter(y => currentAndTwoPriorSchoolYears.includes(y));
    const availableGradeStartingWithLowestGradeWithScores = this.getGradesStartingWithLowestContainingScores(
      availableGrades,
      assessmentScoresUpload
    );
    return assessmentScoresUpload.length &&
      (externalScoresSchoolYears.length > 1 || externalScoresSchoolYears.includes(schoolYear)) ? (
      <div className="program-evaluation-break-page">
        <p>
          <b>Growth on External Measures</b>
        </p>
        {externalScoresSchoolYears.includes(schoolYear) ? (
          <React.Fragment>
            <ul>
              <li>Is the implementation of SpringMath impacting your external measures of math proficiency?</li>
              <li>
                Can you see a difference between groups with low implementation (fewer than 75% of classwide
                intervention skill completed) and those with high implementation (75% or greater classwide interventions
                completed)?
              </li>
            </ul>
            {reporting.renderProficientOnExternalMeasureSpringChart({
              schoolYear,
              assessmentScoresUpload,
              studentGroups,
              assessmentResultsForSite,
              allClasswideStats,
              studentGroupEnrollmentsByGroupId,
              availableGrades: availableGradeStartingWithLowestGradeWithScores,
              gradesWithStudentGroupsInSite,
              isPrinting
            })}
            {reporting.renderProficientOnExternalMeasureFallToSpringChart({
              assessmentScoresUpload,
              schoolYear,
              availableGrades: availableGradeStartingWithLowestGradeWithScores,
              isPrinting
            })}
          </React.Fragment>
        ) : null}
        {externalScoresSchoolYears.length > 1 ? (
          <div>
            {reporting.renderProficientOnExternalMeasureSpringByYearGraph({
              schoolYears: externalScoresSchoolYears,
              assessmentScoresUpload,
              availableGrades: availableGradeStartingWithLowestGradeWithScores,
              isPrinting
            })}
          </div>
        ) : null}
      </div>
    ) : null;
  };

  render() {
    if (this.props.loading) {
      return <Loading message={reporting.loadingMessage} />;
    }
    if (this.props.noDataToRender) {
      return <div className="alert alert-info text-center">No data found for this site</div>;
    }
    const { isPrinting } = this.props;
    const { generatingPrintout } = this.state;

    return (
      <React.Fragment>
        {isPrinting ? null : (
          <button
            className="btn btn-success pull-right print-page program-evaluation-print-this-page"
            onClick={this.printPage}
            disabled={generatingPrintout}
          >
            <i className="fa fa-print" /> {generatingPrintout ? "Preparing printout..." : "Print This Page"}
          </button>
        )}
        <div className={isPrinting ? "" : "conFullScreen"}>
          <div className={isPrinting ? "" : "container report-screen"}>
            {this.renderIntroduction()}
            {!isPrinting ? <br /> : null}
            {this.renderSection1()}
            {!isPrinting ? <br /> : null}
            {this.renderSection2(isPrinting)}
            {!isPrinting ? <br /> : null}
            {this.renderSection3(isPrinting)}
            {!isPrinting ? <br /> : null}
            {this.renderSection4()}
            {!isPrinting ? <br /> : null}
            {this.renderSection5(isPrinting)}
            {!isPrinting ? <br /> : null}
            {this.renderSection6(isPrinting)}
          </div>
        </div>
      </React.Fragment>
    );
  }
}

ProgramEvaluation.propTypes = {
  assessmentGrowths: PropTypes.array,
  allClasswideStats: PropTypes.array,
  assessmentResultsForSite: PropTypes.array,
  assessments: PropTypes.array,
  assessmentScoresUpload: PropTypes.array,
  availableGrades: PropTypes.array,
  benchmarkPeriodsGroupId: PropTypes.string,
  bmPeriods: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  gradesWithStudentGroupsInSite: PropTypes.array,
  growthDataBySchoolYear: PropTypes.object,
  isPrinting: PropTypes.bool,
  loading: PropTypes.bool,
  noDataToRender: PropTypes.bool,
  orgid: PropTypes.string,
  percentMeetingTargetByGradeAndSeason: PropTypes.array,
  ruleAssessmentIdsByGrade: PropTypes.object,
  schoolYear: PropTypes.number,
  site: PropTypes.object,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  studentGroups: PropTypes.array,
  studentGroupsByGrade: PropTypes.object,
  studentsByGroupId: PropTypes.object,
  studentGroupEnrollmentsByGroupId: PropTypes.object,
  users: PropTypes.array
};

const ProgramEvaluationWithTracker = withTracker(params => {
  const { siteId, orgid, schoolYear, studentGroups } = params;
  const isPrinting = isOnPrintPage();
  const ruleAssessmentIdsByGrade = {};
  let assessmentGrowths = [];
  let assessmentScoresUpload = [];
  let assessments = [];
  let availableGrades = [];
  let bmPeriods = [];
  let currentBMPeriod = null;
  let gradesWithStudentGroupsInSite = [];
  let site = {};
  let siteName = "";
  let noDataToRender = false;
  let benchmarkPeriodsGroupId = "default";
  let users = [];
  const assessmentScoresUploadSub = Meteor.subscribe("AssessmentScoresUpload:ForSite", orgid, siteId);
  const assessmentSub = Meteor.subscribe("Assessment:Names");
  const bmpSub = Meteor.subscribe("BenchmarkPeriods");
  const crHandle = Meteor.subscribe("ClasswideRules");
  const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, schoolYear);
  const growthSub = Meteor.subscribe("AssessmentGrowth", "all");
  const orgSub = Meteor.subscribe("Organizations", orgid);
  const sitesSub = Meteor.subscribe("Sites", orgid, siteId);
  const usersSub = Meteor.subscribe("Users", { siteId, orgid });

  const loading = areSubscriptionsLoading(
    assessmentScoresUploadSub,
    assessmentSub,
    bmpSub,
    crHandle,
    gradesSub,
    growthSub,
    orgSub,
    sitesSub,
    usersSub
  );

  if (!loading) {
    const org = Organizations.findOne({ _id: orgid });
    users = Users.find().fetch();

    if (org?.benchmarkPeriodsGroupId) {
      benchmarkPeriodsGroupId = org.benchmarkPeriodsGroupId;
    }
    bmPeriods = BenchmarkPeriods.find().fetch();
    if (siteId && orgid) {
      site = Sites.findOne({ _id: siteId });
      gradesWithStudentGroupsInSite = uniq(studentGroups.map(s => s.grade)).sort((a, b) => {
        return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
      });
      currentBMPeriod = BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
      if (site) {
        siteName = site.name;
      } else {
        noDataToRender = true;
      }
    }
    const rules = Rules.find({ grade: { $exists: true } }).fetch();
    rules.forEach(rule => {
      ruleAssessmentIdsByGrade[rule.grade] = rule.skills.map(r => ({
        assessmentId: r.assessmentId,
        assessmentName: r.assessmentName
      }));
    });
    availableGrades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    assessmentScoresUpload = AssessmentScoresUpload.find().fetch();
    assessments = Assessments.find({}, { fields: { name: 1 } }).fetch();
    assessmentGrowths = AssessmentGrowth.find().fetch();
  }

  return {
    ...params,
    assessmentGrowths,
    assessmentScoresUpload,
    assessments,
    bmPeriods,
    benchmarkPeriodsGroupId,
    currentBMPeriod,
    gradesWithStudentGroupsInSite,
    availableGrades,
    isPrinting,
    loading,
    noDataToRender,
    orgid,
    ruleAssessmentIdsByGrade,
    schoolYear,
    site,
    siteId,
    siteName,
    users
  };
})(ProgramEvaluation);

const ProgramEvaluationDataWrapper = props => {
  const [isFetchingStudents, setIsFetchingStudents] = useState(true);
  const [isFetchingAssessmentResults, setIsFetchingAssessmentResults] = useState(true);
  const [isCalculatingClasswideStats, setIsCalculatingClasswideStats] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [studentsByGroupId, setStudentsByGroupId] = useState([]);
  const [assessmentResultsForSite, setAssessmentResultsForSite] = useState([]);
  const [studentGroupEnrollmentsByGroupId, setStudentGroupEnrollmentsByGroupId] = useState({});
  const [allClasswideStats, setAllClasswideStats] = useState(null);
  const [growthDataBySchoolYear, setGrowthDataBySchoolYear] = useState(null);

  useEffect(() => {
    setIsLoading(true);
  }, [props.schoolYear]);

  useEffect(() => {
    Meteor.call(
      "getGrowthDataBySchoolYearByGradeProgramEvaluation",
      props.schoolYear,
      props.siteId,
      props.orgid,
      (err, resp) => {
        if (!err) {
          setGrowthDataBySchoolYear(resp);
        }
      }
    );
  }, [props.loading, props.schoolYear]);

  useEffect(() => {
    if (props.loading === false) {
      const studentGroupIds = props.studentGroups.map(sg => sg._id);
      setIsFetchingStudents(true);
      Meteor.call("Students:getStudents", studentGroupIds, props.schoolYear, (err, resp) => {
        if (!err) {
          const parsedStudents = resp.reduce((a, c) => {
            if (!a[c.studentGroupId]) {
              // eslint-disable-next-line no-param-reassign
              a[c.studentGroupId] = [];
            }
            a[c.studentGroupId].push(c);
            return a;
          }, {});
          setStudentsByGroupId(parsedStudents);
        }
        setIsFetchingStudents(false);
      });
    }
  }, [props.loading, props.schoolYear]);

  useEffect(() => {
    if (props.loading === false) {
      const studentGroupIds = props.studentGroups.map(sg => sg._id);
      setIsFetchingAssessmentResults(true);
      Meteor.call(
        "AssessmentResults:getAssessmentResultsForStudentGroupIds",
        studentGroupIds,
        props.schoolYear,
        (err, resp) => {
          if (!err) {
            setAssessmentResultsForSite(resp);
          }
          setIsFetchingAssessmentResults(false);
        }
      );
    }
  }, [props.loading, props.schoolYear]);

  useEffect(() => {
    if (props.loading === false) {
      setIsCalculatingClasswideStats(true);
      const studentGroupIds = props.studentGroups.map(sg => sg._id);
      Meteor.call("calculateClasswideStatsForMultipleGroups", studentGroupIds, true, (err, resp) => {
        if (err) {
          utils.ninjalog.error({
            msg: `error getting classwide intervention stats: ${err}`,
            context: "admin"
          });
        } else {
          const { allClasswideResults, studentGroupEnrollments } = resp;
          const sgeByGroupId = studentGroupEnrollments.length
            ? groupBy(
                sortByPropertyFor({ list: studentGroupEnrollments, paths: ["grade"], order: 1 }),
                sge => sge.studentGroupId
              )
            : {};
          setAllClasswideStats(allClasswideResults);
          setStudentGroupEnrollmentsByGroupId(sgeByGroupId);
        }
        setIsCalculatingClasswideStats(false);
      });
    }
  }, [props.loading, props.schoolYear]);

  useEffect(() => {
    if (!isFetchingStudents || !isFetchingAssessmentResults || !isCalculatingClasswideStats) {
      setIsLoading(false);
    }
  }, [isFetchingStudents, isFetchingAssessmentResults, isCalculatingClasswideStats]);

  if (props.loading || isLoading) {
    return <Loading message={reporting.loadingMessage} />;
  }
  return (
    <ProgramEvaluationWithTracker
      {...props}
      studentsByGroupId={studentsByGroupId}
      assessmentResultsForSite={assessmentResultsForSite}
      studentGroupEnrollmentsByGroupId={studentGroupEnrollmentsByGroupId}
      allClasswideStats={allClasswideStats}
      growthDataBySchoolYear={growthDataBySchoolYear}
    />
  );
};

ProgramEvaluationDataWrapper.propTypes = {
  loading: PropTypes.bool,
  studentGroups: PropTypes.array,
  schoolYear: PropTypes.number,
  siteId: PropTypes.string,
  orgid: PropTypes.string
};

const ProgramEvaluationDataWrapperWithTracker = withTracker(params => {
  const { siteId, orgid, schoolYear } = params;
  let sortedStudentGroups = [];
  let studentGroups = [];
  let studentGroupsByGrade = {};
  const studentGroupHandle = Meteor.subscribe("StudentGroups:PerSite", orgid, siteId, schoolYear);
  const loading = areSubscriptionsLoading(studentGroupHandle);

  if (!loading) {
    studentGroups = StudentGroups.find({ siteId, schoolYear })
      .fetch()
      .sort((a, b) => {
        return normalizeGrade(a.grade) < normalizeGrade(b.grade) ? -1 : 1;
      });
    if (studentGroups.length) {
      sortedStudentGroups = sortByPropertyFor({ list: studentGroups, paths: ["grade"], order: 1 });
    }
    studentGroupsByGrade = groupBy(studentGroups, sg => sg.grade);
  }
  return { ...params, studentGroups: sortedStudentGroups, studentGroupsByGrade, loading };
})(ProgramEvaluationDataWrapper);

export default ProgramEvaluationDataWrapperWithTracker;
