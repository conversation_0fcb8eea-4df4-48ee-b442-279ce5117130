import React, { Component } from "react";
import PropTypes from "prop-types";

import { openPrintWindow } from "/imports/ui/utilities";
import { AppDataContext } from "/imports/ui/routing/AppDataContext";
import { SCHOOL_OVERVIEW_TITLE } from "/imports/api/constants";

export default class Header extends Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);
    this.state = {
      isPrinting: false
    };
  }

  componentDidMount() {
    window.onmessage = event => {
      if (event.data === "printScheduled") {
        this.setState({ isPrinting: true });
      } else if (event.data === "printWindowClosed") {
        this.setState({ isPrinting: false });
        window.document.getElementById("print-iframe")?.remove();
        window.document.title = "SpringMath";
      }
    };
  }

  printPage = () => {
    const printURL = `/${this.props.orgid}/print/SchoolOverview?orgid=${this.props.orgid}&gradeId=all&siteId=${this.props.siteId}&screeningHidden=${this.props.screeningHidden}&schoolYear=${this.context.schoolYear}`;
    openPrintWindow(printURL);
  };

  render() {
    if (!this.props.keyLabel || !this.props.headerTitle) {
      return null;
    }

    const shouldShowDemographics =
      this.props.headerTitle === SCHOOL_OVERVIEW_TITLE
        ? this.props.headerStats && this.props.headerStats.some(statObject => statObject.value)
        : true;

    return (
      <div className="conOverviewHeader clearfix">
        <h1 data-testid="groupHeader">
          {this.props.headerTitle} {this.props.headerSubtitle && <small>{this.props.headerSubtitle}</small>}{" "}
          {this.props.additionalHeaderInfo && <small>{this.props.additionalHeaderInfo}</small>}
        </h1>
        <div className="overview-stats">
          {shouldShowDemographics &&
            this.props.headerStats &&
            this.props.headerStats.length > 0 &&
            this.props.headerStats.map((item, i) => (
              <span className="stat" key={`${this.props.keyLabel}_${i}`} data-testid="demographics-header">
                {item.value}
                <small> {item.label}</small>
                {!(this.props.keyLabel === "schoolOverview") || this.props.isPrinting ? null : (
                  <button
                    className="btn btn-success pull-right print-page"
                    disabled={this.state.isPrinting}
                    onClick={this.printPage}
                  >
                    <i className="fa fa-print" /> {this.state.isPrinting ? "Preparing printout..." : "Print This Page"}
                  </button>
                )}
              </span>
            ))}
        </div>
      </div>
    );
  }
}

Header.propTypes = {
  keyLabel: PropTypes.string,
  headerTitle: PropTypes.string,
  headerSubtitle: PropTypes.string,
  headerStats: PropTypes.array,
  additionalHeaderInfo: PropTypes.string,
  isPrinting: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  screeningHidden: PropTypes.bool
};
