import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withRouter } from "react-router-dom";
import { DropdownButton, Dropdown } from "react-bootstrap";
import Alert from "react-s-alert";
import { get } from "lodash";

import IndividualInterventionStudentList from "../individual-intervention/individual-intervention-student-list";
import MeasureResultsDetail from "./measure-results-detail";
import { IndividualScreeningChart } from "../student-detail/individual-screening-chart";
import { isSML } from "/imports/api/utilities/utilities";
import { displayError } from "../student-groups/helperFunction";
import { canForceGroupToClasswideIntervention } from "/imports/api/assessmentResults/helpers";
import { isAdminOrUniversalCoach } from "/imports/api/roles/methods";
import InstructionalVideoModal from "../instructional-video-modal";

class IndividualRecommendation extends Component {
  constructor(props) {
    super(props);
    this.state = {
      checkedStudents: [],
      isModalOpen: false,
      videoId: "",
      videoTimestamp: 0
    };
  }

  numOfStudentAtRisk() {
    return this.props.assessmentResults.classwideResults.studentIdsNotMeetingTarget.length;
  }

  displayStudents() {
    return this.numOfStudentAtRisk() > 1 ? "students " : "student ";
  }

  displayStudentsToWorkWith() {
    return this.numOfStudentAtRisk() > 1 ? "1 or 2 students " : "1 student ";
  }

  getIndividualInterventionRecommendationStudentListData() {
    return this.props.assessmentResults.classwideResults.studentIdsNotMeetingTarget
      .map(studentId => {
        const student = this.props.students.find(s => s._id === studentId);
        if (student) {
          const recommendationReasonData = this.props.assessmentResults.measures.map((measure, index) => {
            const studentResult =
              measure.studentResults && measure.studentResults.find(sResult => sResult.studentId === studentId);
            return {
              reasonTitle: measure.assessmentName || `Measure ${index + 1}`,
              columnData: {
                Score: (studentResult && studentResult.score) || "N/A",
                Target: measure.targetScores[0]
              },
              showRed: !studentResult || !studentResult.score || studentResult.score < measure.targetScores[0]
            };
          });
          return {
            studentName: `${student.identity.name.lastName}, ${student.identity.name.firstName}`,
            studentId,
            recommendationReasonData
          };
        }
        return null;
      })
      .filter(f => f);
  }

  renderScheduleInterventionsButton() {
    if (
      this.props.assessmentResults.classwideResults.studentIdsNotMeetingTarget.length ===
      this.props.scheduledStudentIds.length
    ) {
      return <div className="force-classwide-intervention-btn">{this.getOverrideRulesButton()}</div>;
    }
    return (
      <div className="begin-individual-intervention-row">
        <div className="begin-individual-intervention-box">
          <h4>Next: Administer Individual Interventions</h4>
          {`Skill packets will be provided to help practice foundational math skills. As you complete skills you will
          receive new packets and be able to view your class' skill progress.`}
          <button className="btn btn-success btn-center" onClick={this.assignIndividualIntervention}>
            Begin Individual Interventions
          </button>
          Feeling a little lost? No problem. Get a
          <a
            rel="noopener noreferrer"
            target="_blank"
            href="https://springmath.s3.amazonaws.com/pdf/Conducting%20Individual%20Intervention.pdf"
          >
            {` quick overview `}
          </a>
          of SpringMath.
        </div>
        {this.getOverrideRulesButton()}
      </div>
    );
  }

  processSelectedStudentsIndividualRule = (studentGroupId, siteId) => {
    this.state.checkedStudents.forEach((studentId, index) => {
      Meteor.call(
        "processIndividualRule",
        {
          assessmentResultId: this.props.assessmentResults._id,
          allowNoScore: false,
          studentId
        },
        error => {
          if (error) {
            displayError(`There was an error: ${error.message}`);
          } else if (this.state.checkedStudents.length - 1 === index) {
            this.props.history.push(
              `/${this.props.studentGroup?.orgid}/site/${siteId}/student-groups/${studentGroupId}/individual`
            );
            Alert.success("Individual interventions scheduled successfully", {
              timeout: 3000
            });
          }
        }
      );
    });
  };

  assignIndividualIntervention = () => {
    if (this.state.checkedStudents.length === 0) {
      Alert.error("Please select a student(s)", {
        timeout: 5000
      });
    } else {
      const { _id: studentGroupId, siteId, orgid } = this.props.studentGroup;
      Meteor.call(
        "AssessmentResult:hasGroupOpenIndividualIntervention",
        studentGroupId,
        siteId,
        orgid,
        this.showInstructionalVideoForNewIntervention
      );
    }
  };

  showInstructionalVideoForNewIntervention = (
    errorCheckingForOpenInterventions /* , hasOpenIndividualIntervention */
  ) => {
    const { _id: studentGroupId, siteId } = this.props.studentGroup;
    if (errorCheckingForOpenInterventions) {
      Alert.error("There was a problem checking for open interventions.", {
        timeout: 5000
      });
    }
    // if (!hasOpenIndividualIntervention) {
    //   const videoType = "individual";
    //   return Meteor.call(
    //     "InstructionalVideos:getIdFromVideoUrl",
    //     videoType,
    //     siteId,
    //     (error, { videoId, videoTimestamp }) => {
    //       if (error) {
    //         Alert.error("There was a problem fetching video url.", {
    //           timeout: 5000
    //         });
    //       }
    //       return this.openModal({
    //         videoId,
    //         videoTimestamp
    //       });
    //     }
    //   );
    // }
    return this.processSelectedStudentsIndividualRule(studentGroupId, siteId);
  };

  closeModal = () => {
    this.setState({ isModalOpen: false });
  };

  openModal = ({ videoId = "", videoTimestamp }) => {
    this.setState({ isModalOpen: true, videoId, videoTimestamp });
  };

  addStudent = (isChecked, studentId) => {
    this.setState(prevState => ({
      ...prevState,
      checkedStudents: isChecked
        ? [...prevState.checkedStudents, studentId]
        : prevState.checkedStudents.filter(sId => sId !== studentId)
    }));
  };

  getMeasureIndexList = () => {
    return [...Array(this.props.assessmentResults.measures.length).keys()];
  };

  getMeasureResultsDetail = () => {
    if (this.props.isPrinting) {
      return this.getMeasureIndexList().map(num => {
        return (
          <div
            key={`chart_${num}`}
            className="page-break-after screening-page-graph"
            data-testid={`individual-screening-graph_${num}`}
          >
            <MeasureResultsDetail assessmentResults={this.props.assessmentResults} selectedMeasureIndex={num} />
          </div>
        );
      });
    }

    return (
      <MeasureResultsDetail
        assessmentResults={this.props.assessmentResults}
        selectedMeasureIndex={this.props.selectedAssessment}
      />
    );
  };

  renderChart = () => {
    const currentBenchmark = (this.props.studentGroup.history || []).find(
      sgh => sgh.type === "benchmark" && sgh.whenEnded
    );
    const studentId = this.props.students[0]._id;
    const measureScores =
      currentBenchmark &&
      currentBenchmark.assessmentResultMeasures.map(hm => {
        const studentResult = hm.studentResults.find(sr => sr.studentId === studentId);
        return studentResult ? parseInt(studentResult.score) : null;
      });

    return isSML(this.props.students[0].orgid) ? (
      <IndividualScreeningChart
        bmScores={currentBenchmark}
        chartId={this.props.assessmentResults._id}
        measureScores={measureScores}
      />
    ) : (
      this.getMeasureResultsDetail()
    );
  };

  // eslint-disable-next-line no-confusing-arrow
  getOverrideRulesButton = () =>
    this.canForceClasswideIntervention() ? (
      <div className="override-rules-button">
        <DropdownButton
          variant="default"
          title="⋯"
          className="no-caret"
          onSelect={this.forceClasswideIntervention}
          id="force-classwide-intervention-dropdown"
        >
          <Dropdown.Item eventKey="force-classwide-intervention">
            Override Recommendation and Schedule Classwide Intervention
          </Dropdown.Item>
        </DropdownButton>
      </div>
    ) : null;

  forceClasswideIntervention = () => {
    const benchmarkAssessmentResultId = this.props.assessmentResults._id;
    const { _id: studentGroupId, siteId, orgid: orgId } = this.props.studentGroup;
    Meteor.call("forceClasswideInterventionFor", { studentGroupId, benchmarkAssessmentResultId, siteId }, err => {
      if (err) {
        Alert.error("There was a problem while forcing classwide intervention. Please contact the SpringMath admin.", {
          timeout: 5000
        });
      } else {
        this.props.history.push(`/${orgId}/site/${siteId}/student-groups/${studentGroupId}/classwide`);
      }
    });
  };

  canForceClasswideIntervention = () => {
    if (this.props.assessmentResults.classwideResults.totalStudentsAssessedOnAllMeasures === 1) {
      return false;
    }
    const isAdminUser = isAdminOrUniversalCoach(this.props.studentGroup.siteId, this.props.user.profile.siteAccess);
    if (!isAdminUser) {
      return false;
    }
    const isViewingCurrentBenchmark =
      this.props.currentBenchmarkPeriodId === this.props.assessmentResults.benchmarkPeriodId;
    if (!this.props.isInCurrentSchoolYear || !isViewingCurrentBenchmark) {
      return false;
    }
    return canForceGroupToClasswideIntervention(this.props.studentGroup);
  };

  getClasswideScreeningSummaryText = () => (
    <p>
      {this.props.isClasswideInterventionComplete ? (
        <span>Although your class has already completed all of the classwide interventions, we found</span>
      ) : (
        <span>Although your class is doing well, we found</span>
      )}{" "}
      <strong>
        {this.numOfStudentAtRisk()} {this.displayStudents()}
      </strong>
      who could benefit from intervention. Intervention takes 15-20 minutes a day per student, so we recommend selecting{" "}
      {this.displayStudentsToWorkWith()} to work with.
    </p>
  );

  getIndividualScreeningSummaryText = () => (
    <p>
      <em>
        We found {this.numOfStudentAtRisk()} {this.displayStudents()} who could benefit from intervention.
      </em>
      &nbsp; Intervention takes 15-20 minutes a day per student, so we recommend selecting{" "}
      {this.displayStudentsToWorkWith()} to work with.
    </p>
  );

  getIndividualInterventionsSummary = () => {
    if (this.props.onlyOneStudentTookAndFailedBenchmark) {
      return <p>Individual intervention takes 15-20 minutes a day.</p>;
    }
    return this.props.classwideEnabled
      ? this.getClasswideScreeningSummaryText()
      : this.getIndividualScreeningSummaryText();
  };

  render() {
    const hasNoClasswideInterventionYet =
      !this.props.firstClasswideInterventionCreatedAt ||
      this.props.firstClasswideInterventionCreatedAt > this.props.assessmentResults.lastModified.on;
    if (
      hasNoClasswideInterventionYet &&
      this.props.assessmentResults.classwideResults.studentIdsNotMeetingTarget.length > 0
    ) {
      // create a map of students and measure results indicating why they've been recommended
      const individualInterventionRecommendationStudentListData = this.getIndividualInterventionRecommendationStudentListData();
      const schedulingFromHereEnabled =
        this.props.isInCurrentSchoolYear &&
        this.props.currentBenchmarkPeriodId === this.props.assessmentResults.benchmarkPeriodId;
      const { _id: studentGroupId, siteId } = this.props.studentGroup;
      return (
        <div className="intervention-recommendation">
          {!this.props.noIndividualScreeningResults && (
            <div id="individual-screening-results">
              {this.renderChart()}
              {this.props.isPrinting ? null : <hr />}
            </div>
          )}
          {get(this.props.studentGroup.individualInterventionQueue, "length") ? (
            <React.Fragment>
              <div id="individual-screening-summary">
                <h5>
                  <strong>Individual Interventions</strong>
                </h5>
                {this.getIndividualInterventionsSummary()}
              </div>
              {this.props.isPrinting ? null : (
                <div id="individual-intervention-recommendation" data-testid="individual-intervention-recommendation">
                  <h6>
                    Students listed in recommended priority. Please select {this.displayStudentsToWorkWith()} for
                    intervention. <br />
                    It is recommended that you select no more than 2 students to ensure intervention integrity.
                  </h6>
                  <IndividualInterventionStudentList
                    recommendationData={individualInterventionRecommendationStudentListData}
                    addStudent={this.addStudent}
                    schedulingEnabled={schedulingFromHereEnabled}
                    scheduledStudentIds={this.props.scheduledStudentIds}
                    checkedStudents={this.state.checkedStudents}
                  />
                </div>
              )}
            </React.Fragment>
          ) : null}
          {!this.props.isPrinting && schedulingFromHereEnabled && this.renderScheduleInterventionsButton()}
          {this.state.isModalOpen ? (
            <InstructionalVideoModal
              showModal={this.state.isModalOpen}
              closeModal={this.closeModal}
              onCloseModal={() => this.processSelectedStudentsIndividualRule(studentGroupId, siteId)}
              videoId={this.state.videoId}
              videoTimestamp={this.state.videoTimestamp}
              headerText="If this is your first time using individual intervention please watch this video."
              testIdPrefix="individual-intervention"
            />
          ) : null}
        </div>
      );
    }

    // No students need interventions!
    return (
      <div className="intervention-recommendation">
        <div id="individual-screening-results">
          {this.renderChart()}
          <hr />
          {isSML(this.props.students[0].orgid) ? (
            <div>
              <h4 className="w9">
                We recommend leaving student enrolled in SpringMath Lite and screening her/him again at the next
                benchmark screening season. Student passed all of the screening assessments and does not need an
                individual intervention.
              </h4>
            </div>
          ) : (
            <div>
              <h4 className="w9">Your class is doing awesome!</h4>
              <div className="begin-individual-intervention-row">
                <p>We did not identify any students who need interventions at this time.</p>
                {this.getOverrideRulesButton()}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

IndividualRecommendation.propTypes = {
  assessmentResults: PropTypes.object,
  students: PropTypes.array,
  currentBenchmarkPeriodId: PropTypes.string,
  classwideEnabled: PropTypes.bool,
  selectedAssessment: PropTypes.number,
  scheduledStudentIds: PropTypes.array,
  studentGroup: PropTypes.object,
  isInCurrentSchoolYear: PropTypes.bool,
  onlyOneStudentTookAndFailedBenchmark: PropTypes.bool,
  user: PropTypes.object,
  isPrinting: PropTypes.bool,
  history: PropTypes.object,
  firstClasswideInterventionCreatedAt: PropTypes.number,
  isClasswideInterventionComplete: PropTypes.bool,
  noIndividualScreeningResults: PropTypes.bool
};

export default withRouter(IndividualRecommendation);
