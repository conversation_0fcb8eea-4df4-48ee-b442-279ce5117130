import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON>, withRouter } from "react-router-dom";
import { Button, Dropdown, DropdownButton } from "react-bootstrap";

import { PRINT_OPTIONS } from "/imports/api/constants";
import MessageNotice from "../components/message-notices/message-notice.jsx";
import DetailContentLayout from "./detail-content-layout";
import DetailHeaderText from "./detail-header-text";
import { isHighSchoolGrade, openPrintWindow } from "../utilities";
import { ClassContext } from "../pages/classContext";

class DetailLayout extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isPrinting: false,
      selectedPrintOption: PRINT_OPTIONS.CURRENT_CLASSWIDE_INTERVENTION
    };
  }

  componentDidMount() {
    window.onmessage = event => {
      if (event.data === "printScheduled") {
        this.setState({ isPrinting: true });
      } else if (event.data === "printWindowClosed") {
        this.setState({ isPrinting: false });
        window.document.getElementById("print-iframe")?.remove();
        window.document.title = "SpringMath";
      }
    };
  }

  printPage = () => {
    const printURL = `/${this.props.studentGroup.orgid}/print/StudentDetail?siteId=${this.props.siteId}&studentGroupId=${this.props.studentGroupId}&studentId=${this.props.studentId}&schoolYear=${this.props.studentGroup.schoolYear}&orgid=${this.props.studentGroup.orgid}&printOption=${this.state.selectedPrintOption}`;
    openPrintWindow(printURL);
  };

  shouldRenderIndividualProgress() {
    return this.context.student?.history?.length > 0;
  }

  changePrintOption = printOptionName => {
    this.setState({ selectedPrintOption: printOptionName });
  };

  getButtonClassname = shouldBeDisabled => {
    return `btn btn-success${shouldBeDisabled ? " cursor-not-allowed" : ""}`;
  };

  redirectToStudent = direction => () => {
    const { siteId, studentGroupId, studentId, students } = this.props;
    const targetStudentId =
      students[students.findIndex(s => s._id === studentId) + (direction === "next" ? 1 : -1)]?._id;
    const individualSectionSuffix = window.location.href.includes("/individual") ? "/individual" : "";
    return this.props.history.push(
      `/${this.props.studentGroup.orgid}/site/${siteId}/student-groups/${studentGroupId}/students/${targetStudentId}${individualSectionSuffix}`
    );
  };

  render() {
    const {
      students,
      studentId,
      expandedNoticeState,
      expandedStateCB,
      context,
      studentGroupId,
      content,
      siteId,
      studentGroup
    } = this.props;
    const isHighSchoolGroup = isHighSchoolGrade(studentGroup.grade);
    const isFirstStudentInQueue = students.findIndex(s => s._id === studentId) === 0;
    const isLastStudentInQueue = students.findIndex(s => s._id === studentId) === students.length - 1;
    return (
      <div className="workspace-container profile-view">
        <div className="d-flex justify-content-between mt-1">
          <Link
            className="btn btn-success"
            to={`/${studentGroup.orgid}/site/${siteId}/student-groups/${studentGroupId}/students`}
          >
            <i className="fa fa-arrow-left" /> Back to All Students
          </Link>
          {students.length <= 1 ? null : (
            <div className="d-flex gap-1">
              <Button
                className={this.getButtonClassname(isFirstStudentInQueue)}
                disabled={isFirstStudentInQueue}
                onClick={this.redirectToStudent("previous")}
              >
                <i className="fa fa-arrow-left" /> Previous Student
              </Button>
              <Button
                className={this.getButtonClassname(isLastStudentInQueue)}
                disabled={isLastStudentInQueue}
                onClick={this.redirectToStudent("next")}
              >
                Next Student <i className="fa fa-arrow-right" />
              </Button>
            </div>
          )}
          {context === "student-detail" && (
            <div>
              <button
                className="btn btn-success pull-right print-page"
                disabled={this.state.isPrinting}
                onClick={this.printPage}
              >
                <i className="fa fa-print" />
                &nbsp;
                {this.state.isPrinting ? "Preparing printout..." : "Print"}
              </button>
              <div className="pull-right btn-group m-r-5">
                <DropdownButton variant="default" title="Print Options">
                  {Object.entries(PRINT_OPTIONS).map(([printOptionId, printOptionName]) => (
                    <Dropdown.Header key={printOptionId}>
                      <label>
                        <input
                          type="radio"
                          checked={this.state.selectedPrintOption === printOptionName}
                          onChange={() => this.changePrintOption(printOptionName)}
                          className="me-2"
                        />
                        <big>{printOptionName}</big>
                      </label>
                    </Dropdown.Header>
                  ))}
                </DropdownButton>
              </div>
            </div>
          )}
        </div>

        <DetailHeaderText
          context={context}
          studentGroup={studentGroup}
          displayStats={this.shouldRenderIndividualProgress()}
        />

        {isHighSchoolGroup ? null : (
          <MessageNotice
            noticeLocation="side-nav-layout"
            expandedStateCB={expandedStateCB}
            expandedNoticeState={expandedNoticeState}
          />
        )}
        <DetailContentLayout content={content} />
      </div>
    );
  }
}

DetailLayout.propTypes = {
  studentId: PropTypes.string,
  studentGroup: PropTypes.object,
  siteId: PropTypes.string,
  studentGroupId: PropTypes.string,
  content: PropTypes.object,
  context: PropTypes.string,
  expandedStateCB: PropTypes.func,
  messageNotice: PropTypes.object,
  expandedNoticeState: PropTypes.bool,
  history: PropTypes.object,
  students: PropTypes.array
};

DetailLayout.contextType = ClassContext;

export default withRouter(DetailLayout);
