import some from "lodash/some";
import find from "lodash/find";
import filter from "lodash/filter";
import get from "lodash/get";
import each from "lodash/each";
import includes from "lodash/includes";
import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { findLast } from "lodash";
import sortBy from "lodash/sortBy";
import { ScreeningAssignments } from "../../screeningAssignments/screeningAssignments";
import { Interventions } from "../../interventions/interventions";
import { Rules } from "../../rules/rules";
import { Assessments } from "../../assessments/assessments";
import * as assessmentsMethods from "../../assessments/methods";
import { AssessmentResults } from "../../assessmentResults/assessmentResults";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { Students } from "../../students/students";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { getCurrentScho<PERSON><PERSON>ear, getMeteor<PERSON>ser, getMeteorUserId, ninjalog, translateBenchmarkPeriod } from "../utilities";
import * as auth from "../../authorization/server/methods";
import { hasSiteAccess } from "../../authorization/server/methods";
import { isHighSchoolGrade } from "../../../ui/utilities";
import BenchmarkPeriodHelpers from "../../benchmarkPeriods/methods";
import { removeStudentFromAllSkillGroupsForSite } from "../../studentsBySkill/methods";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";
import { getIndividualResultsMessage } from "../../assessmentResults/utilities";
import { createNewIndividualInterventionAssessmentResultForStudent } from "../../assessmentResults/createNewIndividualInterventionAssessmentResultForStudent";
import { getCurrentDate } from "../../helpers/getCurrentDate";

export async function isTakingOnlyBenchmarkInDrillDownMode(
  grade,
  benchmarkPeriodLabel,
  firstMeasureAssessmentId,
  assessmentResultRootRuleId
) {
  const allCurrentRules = await Rules.find({
    "attributeValues.grade": grade,
    "attributeValues.benchmarkPeriod": benchmarkPeriodLabel
  }).fetchAsync();
  const allCurrentRootRules = allCurrentRules.filter(r => r._id === r.rootRuleId);
  const potentialRootRule = allCurrentRootRules.find(
    rr => rr.attributeValues.assessmentId === firstMeasureAssessmentId
  );
  return potentialRootRule && assessmentResultRootRuleId === potentialRootRule._id;
}

export async function assignNextNode({
  failedResult,
  failedMeasure,
  assessmentResult,
  studentGroup,
  studentId,
  isResuming
}) {
  let targetResult = failedResult;
  let targetMeasure = failedMeasure;
  const benchmarkPeriodToUse = isHighSchoolGrade(studentGroup.grade)
    ? "allPeriods"
    : assessmentResult.benchmarkPeriodId;
  const currentScreeningAssignment = await ScreeningAssignments.findOneAsync({
    grade: assessmentResult.grade,
    benchmarkPeriodId: benchmarkPeriodToUse
  });
  // set the targets to the non-benchmark assessment targets if in individual
  if (!targetResult && assessmentResult.type === "individual" && assessmentResult.measures?.length) {
    targetMeasure = assessmentResult.measures.length > 1 ? assessmentResult.measures[1] : assessmentResult.measures[0];
    [targetResult] = [targetMeasure?.studentResults[0]];
  }

  if (!targetMeasure) {
    throw Error("Missing targetMeasure for intervention. Check calling function logic.");
  }

  const rulesQ = {
    "attributeValues.assessmentId": targetMeasure.assessmentId,
    "attributeValues.benchmarkPeriod": translateBenchmarkPeriod(benchmarkPeriodToUse).label,
    "attributeValues.grade": studentGroup.grade
  };

  if (assessmentResult.type === "individual") {
    rulesQ.rootRuleId = assessmentResult.rootRuleId;
  }
  const rules = await Rules.find(rulesQ).fetchAsync();
  const rootRuleIds = rules.map(r => r.rootRuleId);
  const rootRules = await Rules.find({
    _id: { $in: rootRuleIds }
  }).fetchAsync();
  const rootRulesAssessmentIds = rootRules.map(r => r.attributeValues.assessmentId);

  const studentAssessmentResultsForTheseRules = await AssessmentResults.find({
    "measures.assessmentId": { $in: rootRulesAssessmentIds },
    "measures.studentResults.studentId": studentId,
    benchmarkPeriodId: benchmarkPeriodToUse,
    schoolYear: assessmentResult.schoolYear
  }).fetchAsync();
  // need to add the current in-memory result to the list here...
  studentAssessmentResultsForTheseRules.push(assessmentResult);

  // found all assessment results for these assessment ids where this particular
  // student failed at least on of these assessments
  // now filter the results down to the actual measures this particular student failed
  // (( in lieu of a complicated query/projection ))
  const studentFailingAssessmentMeasures = [];
  const studentPassingAssessmentIds = [];
  each(studentAssessmentResultsForTheseRules, far => {
    each(far.measures, m => {
      if (includes(rootRulesAssessmentIds, m.assessmentId)) {
        const studentResult = find(m.studentResults, sr => sr.studentId === studentId);
        if (studentResult && !studentResult.meetsTarget) {
          studentFailingAssessmentMeasures.push(m);
        }
        if (studentResult && studentResult.meetsTarget && !["classwide", "benchmark"].includes(far.type)) {
          studentPassingAssessmentIds.push(m.assessmentId);
        }
      }
    });
  });

  // remove ones they did eventually pass at some point...
  const studentNeverPassedAssessmentMeasures = [];
  each(studentFailingAssessmentMeasures, fam => {
    if (!includes(studentPassingAssessmentIds, fam.assessmentId)) {
      studentNeverPassedAssessmentMeasures.push(fam);
    }
  });

  const currentRootRuleAssessmentId =
    find(currentScreeningAssignment.assessmentIds, aid =>
      includes(
        studentNeverPassedAssessmentMeasures.map(fam => fam.assessmentId),
        aid
      )
    ) || get(assessmentResult, "individualSkills.benchmarkAssessmentId");

  const rootRule = find(rootRules, r => r.attributeValues.assessmentId === currentRootRuleAssessmentId);

  const rule = find(rules, r => rootRule && r.rootRuleId === rootRule._id);

  if (!rule) {
    throw Error("Could not find a rule to apply to this assessment score");
  }

  const passed = targetResult.individualRuleOutcome === "above";
  const outcome = rule.outcomes[targetResult.individualRuleOutcome];

  // make assignment
  // hold on... have we been down this route or better before??
  // lets see.

  let routeFound = false;
  let currentFoundAssessmentId = targetMeasure.assessmentId;
  const allIndividualAssessmentResultsForStudentAndPeriod = await AssessmentResults.find({
    benchmarkPeriodId: benchmarkPeriodToUse,
    type: "individual",
    schoolYear: assessmentResult.schoolYear,
    "measures.studentResults.studentId": studentId
  }).fetchAsync();

  // ****************************** FIND BEST PATH AND INTERVENTIONS IF NECESSARY ******************
  let ruleResultsToUse = null;

  const student = await Students.findOneAsync(studentId);
  const { currentSkill } = student;

  let iter = 0;
  const failSafeCount = 45;
  while (!routeFound && ++iter < failSafeCount) {
    const bestAssScore = {
      score: 0,
      assessmentResultId: "",
      individualRuleOutcome: ""
    };
    // eslint-disable-next-line no-loop-func
    allIndividualAssessmentResultsForStudentAndPeriod.forEach(ar => {
      const measuresForAss = filter(ar.measures, m => {
        const studentHistory = student.history.filter(sh => sh.assessmentResultId === ar._id);
        return (
          m.assessmentId === currentFoundAssessmentId &&
          some(m.studentResults, sr => sr.studentId === studentId) &&
          studentHistory.benchmarkAssessmentId === currentRootRuleAssessmentId
        );
      });
      const studentResults = measuresForAss.map(m => find(m.studentResults, sr => sr.studentId === studentId));
      studentResults.forEach(sr => {
        if (Number(sr.score) >= Number(bestAssScore.score)) {
          bestAssScore.score = sr.score;
          bestAssScore.assessmentResultId = ar._id;
          bestAssScore.individualRuleOutcome = sr.individualRuleOutcome;
        }
      });
    });
    ninjalog.log({
      msg: "bestAssScore",
      val: bestAssScore,
      jsonString: true,
      context: "individual"
    });
    const bestAssResult = find(
      allIndividualAssessmentResultsForStudentAndPeriod,
      ar => ar._id === bestAssScore.assessmentResultId
    );
    ninjalog.log({
      msg: "bestAssResult",
      val: bestAssResult,
      jsonString: true,
      context: "individual"
    });

    const ruleResults = bestAssResult && bestAssResult.ruleResults && bestAssResult.ruleResults.nextSkill;
    if (
      ruleResults &&
      (!ruleResults.interventions || (ruleResults.interventions && ruleResults.interventions.length === 0))
    ) {
      currentFoundAssessmentId = ruleResults.assessmentId;
      // if we found an assessment that we could have done better on, stop travelling and assign it
      if (!ruleResults.passed) {
        if (bestAssResult.individualRuleOutcome !== "") {
          ruleResultsToUse = bestAssResult.ruleResults;
          outcome.interventionIds = ruleResultsToUse.nextSkill.interventions.map(i => i.interventionId);
          outcome.assessmentId = ruleResultsToUse.nextSkill.assessmentId;
        }
        routeFound = true;
      }
    } else {
      if (ruleResults && ruleResults.interventions && ruleResults.interventions.length > 0) {
        currentFoundAssessmentId = ruleResults.assessmentId;
        // find new rule as well...
        if (bestAssResult.individualRuleOutcome !== "") {
          ruleResultsToUse = bestAssResult.ruleResults;
          outcome.interventionIds = ruleResultsToUse.nextSkill.interventions.map(i => i.interventionId);
          outcome.assessmentId = ruleResultsToUse.nextSkill.assessmentId;
        }
      }
      routeFound = true;
    }
  }

  // Now wait just a minute!  What if they are working on a screening assignment with interventions?
  // Check if they have already passed the next assessment and put them there instead?

  // Wait again!!! Why can't we just do this...If they score below on the current assessment and they are coming from one with interventions,
  // reassign the same damn thing!

  // TODO - While this works for the current trees, we should be solving the tree traversal in a more general sense so that it is not possible to
  // build a tree that our logic doesn't work with.
  if (
    (targetResult.individualRuleOutcome === "below" &&
      currentSkill &&
      currentSkill.assessmentId !== outcome.assessmentId &&
      currentSkill.interventions?.length > 0) ||
    (isResuming && student?.history?.[0]?.interventions?.length && targetResult.individualRuleOutcome !== "above")
  ) {
    const belowRuleQuery = {
      rootRuleId: rule.rootRuleId,
      "attributeValues.grade": rule.attributeValues.grade,
      "attributeValues.benchmarkPeriod": rule.attributeValues.benchmarkPeriod,
      "outcomes.above.assessmentId": rule.attributeValues.assessmentId,
      "outcomes.above.interventionIds.0": { $exists: true }
    };
    const belowRule = await Rules.findOneAsync(belowRuleQuery);
    if (belowRule) {
      routeFound = true;
      outcome.assessmentId = belowRule.outcomes.above.assessmentId;
      outcome.interventionIds = belowRule.outcomes.above.interventionIds;
      outcome.rootAssessmentId = currentRootRuleAssessmentId;
    }
  }

  // ****************************** END FIND BEST PATH AND INTERVENTIONS IF NECESSARY **************

  const rootRuleId = rootRule._id;
  const newAssessmentToCheck =
    outcome && outcome.assessmentId ? await Assessments.findOneAsync({ _id: outcome.assessmentId }) : null;
  const rootAssessment = await Assessments.findOneAsync({
    _id: currentRootRuleAssessmentId
  });

  let newAssessment = null;
  // final check if assessment was found
  if (newAssessmentToCheck) {
    newAssessment = newAssessmentToCheck;
  } else {
    throw Error(
      `Could not find next assessment for outcome: ${targetResult.individualRuleOutcome}. Check rule '${rootRuleId}' with decision tree`
    );
  }

  const rootAssessmentName = (rootAssessment && rootAssessment.name) || "";

  if (assessmentResult.type === "classwide") {
    await Meteor.callAsync("StudentGroupEnrollments:isInIndividualIntervention", {
      studentGroupId: assessmentResult.studentGroupId,
      studentId
    });
  }

  const interventionIds =
    outcome && outcome.interventionIds && outcome.interventionIds !== "" ? outcome.interventionIds : [];
  const interventions = [];
  const theInterventions = await Interventions.find({
    _id: { $in: interventionIds }
  }).fetchAsync();

  theInterventions.forEach(int => {
    interventions.push({
      interventionId: int._id,
      interventionLabel: int.name,
      interventionAbbrv: int.abbreviation
    });
  });
  ninjalog.log({
    msg: "targetMeasure",
    val: targetMeasure,
    jsonString: true,
    context: "individual"
  });

  const skills = {
    benchmarkAssessmentId: currentRootRuleAssessmentId,
    benchmarkAssessmentName: rootAssessmentName,
    benchmarkAssessmentTargets: assessmentsMethods.getScoreTargets({
      assessment: rootAssessment,
      grade: assessmentResult.grade,
      benchmarkPeriodId: benchmarkPeriodToUse,
      assessmentType: "individual"
    }),
    assessmentId: newAssessment._id,
    assessmentName: newAssessment.name,
    assessmentTargets: assessmentsMethods.getScoreTargets({
      assessment: newAssessment,
      grade: assessmentResult.grade,
      benchmarkPeriodId: benchmarkPeriodToUse,
      assessmentType: "individual"
    }),
    interventions: interventions || []
  };

  const newAssessmentResult = await createNewIndividualInterventionAssessmentResultForStudent({
    previousAssessmentResult: assessmentResult,
    studentId,
    skills,
    rootRuleId
  });
  const lastModified = await getTimestampInfo(getMeteorUserId(), assessmentResult?.orgid, "assignNextNode");
  await StudentGroups.updateAsync(studentGroup._id, {
    $set: {
      individualInterventionQueue: (studentGroup.individualInterventionQueue || []).filter(sId => sId !== studentId),
      lastModified
    }
  });

  const nextSkill = {
    assessmentId: newAssessment._id,
    assessmentName: newAssessment.name,
    interventions: interventions || []
  };

  ninjalog.log({
    msg: "ruleResultsToUse",
    val: ruleResultsToUse,
    jsonString: true,
    context: "individual"
  });

  return {
    passed,
    nextSkill: ruleResultsToUse || nextSkill,
    nextAssessmentResultId: newAssessmentResult._id
  };
}

// TODO further refactor
export async function assignNextTree(assessmentResult, studentGroup, studentId, shouldHSStudentContinue) {
  const userId = getMeteorUserId();
  const screeningAssignments = await ScreeningAssignments.findOneAsync({
    benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
    grade: studentGroup.grade
  });

  const isStudentScheduledFromFourWeekRuleInHS =
    assessmentResult.grade === "HS" && studentGroup.individualInterventionQueue?.includes(studentId);
  const nextAssessmentIdIndex =
    screeningAssignments.assessmentIds.findIndex(assId => assId === assessmentResult.measures[0].assessmentId) +
    (isStudentScheduledFromFourWeekRuleInHS ? 0 : 1);
  let nextBenchmarkRule = null;
  const nextAssessmentId = screeningAssignments.assessmentIds[nextAssessmentIdIndex];
  let allRulesForNextAssessment;
  if (nextAssessmentId) {
    const nextRootRuleQuery = {
      "attributeValues.grade": assessmentResult.grade,
      "attributeValues.benchmarkPeriod": translateBenchmarkPeriod(assessmentResult.benchmarkPeriodId).label,
      "attributeValues.assessmentId": nextAssessmentId
    };
    allRulesForNextAssessment = await Rules.find(nextRootRuleQuery).fetchAsync();
    if (assessmentResult.grade === "HS" && isStudentScheduledFromFourWeekRuleInHS) {
      const rootRule = allRulesForNextAssessment.find(rule => rule._id === rule.rootRuleId);
      // NOTE(fmazur) - Use one node below root node
      nextBenchmarkRule = await Rules.findOneAsync({
        ...nextRootRuleQuery,
        "attributeValues.assessmentId": rootRule.outcomes.below.assessmentId,
        rootRuleId: rootRule._id
      });
    } else {
      nextBenchmarkRule = allRulesForNextAssessment.find(rule => rule._id === rule.rootRuleId);
    }
  }

  const student = await Students.findOneAsync(assessmentResult.studentId);
  const timestampInfo = await getTimestampInfo(userId, assessmentResult?.orgid, "assignNextTree");
  /** ***********************************************************
   // Student is done with all individual trees...
   ************************************************************ */
  if (
    !nextBenchmarkRule ||
    (assessmentResult.grade === "HS" && student?.currentSkill?.assessmentId && !shouldHSStudentContinue)
  ) {
    ninjalog.log({
      msg:
        "Could not find a next benchmark rule to apply to this assessment score." +
        "Should be all done with individual interventions!",
      context: "individual"
    });
    ninjalog.log({
      msg: "Student has finished individual trees, about to update skill on student object.",
      context: "individual"
    });
    // Student updates
    if (!student.history) {
      student.history = [];
    }
    if (
      assessmentResult.type === "individual" &&
      assessmentResult.studentId &&
      assessmentResult.classwideResults.totalStudentsMeetingAllTargets > 0 &&
      (student.history[0]?.assessmentId !== assessmentResult.individualSkills?.assessmentId ||
        student.history[0]?.message?.messageCode !== "56")
    ) {
      const message = getIndividualResultsMessage({
        currentSkill: null,
        history: student.history
      });
      if (student.currentSkill) {
        // shouldn't need to do this... but in the essence of time
        student.currentSkill.assessmentResultMeasures = assessmentResult.measures;
        student.currentSkill.type = assessmentResult.type;
        student.currentSkill.benchmarkPeriodId = assessmentResult.benchmarkPeriodId;
        student.currentSkill.whenEnded = timestampInfo;
        student.currentSkill.message = message;
        student.history.unshift(student.currentSkill);
      }
      await Students.updateAsync(assessmentResult.studentId, {
        $set: {
          history: student.history,
          currentSkill: {
            whenStarted: timestampInfo,
            benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
            message
          },
          lastModified: timestampInfo
        }
        // $unset: {
        //   currentSkill: 1,
        // },
      });
      // remove this assessmentResult id from the student group current assessmentResult ids
      if (studentGroup && studentGroup.currentAssessmentResultIds) {
        const newAssessmentResultIds = studentGroup.currentAssessmentResultIds.filter(
          ar => ar !== assessmentResult._id
        );
        await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
          $set: {
            currentAssessmentResultIds: newAssessmentResultIds,
            lastModified: timestampInfo
          }
        });
      }
    }
    // Return control to calling function as there is nothing else to do here
    return { passed: true, nextSkill: null };

    /** ***********************************************************
     * Student has another individual tree to work on
     ************************************************************ */
  }

  const nextBenchmarkAssessment = await Assessments.findOneAsync(nextBenchmarkRule.attributeValues.assessmentId);
  let rootNodeBenchmarkAssessment = nextBenchmarkAssessment;
  if (assessmentResult.grade === "HS") {
    const rootRule = allRulesForNextAssessment.find(rule => rule._id === rule.rootRuleId);
    rootNodeBenchmarkAssessment = await Assessments.findOneAsync(rootRule.attributeValues.assessmentId);
  }
  // Check for Errors
  if (nextBenchmarkRule && !nextBenchmarkAssessment) {
    throw Error(`Could not find next benchmark assessment for rule: ${nextBenchmarkRule._id}`);
    // Everything looks good, continue with assignment
  }

  const nextBenchmarkSkill = {
    assessmentId: nextBenchmarkAssessment._id,
    assessmentName: nextBenchmarkAssessment.name,
    interventions: []
  };
  // need to create a new assessment result
  const newAssRes = await createNewIndividualInterventionAssessmentResultForStudent({
    previousAssessmentResult: assessmentResult,
    studentId,
    skills: {
      benchmarkAssessmentId: rootNodeBenchmarkAssessment._id,
      benchmarkAssessmentName: rootNodeBenchmarkAssessment.name,
      benchmarkAssessmentTargets: assessmentsMethods.getScoreTargets({
        assessment: rootNodeBenchmarkAssessment,
        grade: assessmentResult.grade,
        benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
        assessmentType: "individual"
      }),
      assessmentId: nextBenchmarkAssessment._id,
      assessmentName: nextBenchmarkAssessment.name,
      assessmentTargets: assessmentsMethods.getScoreTargets({
        assessment: nextBenchmarkAssessment,
        grade: assessmentResult.grade,
        benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
        assessmentType: "individual"
      }),
      interventions: nextBenchmarkAssessment.interventions
    },
    rootRuleId: assessmentResult.grade === "HS" ? nextBenchmarkRule.rootRuleId : nextBenchmarkRule._id
  });
  await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
    $set: {
      individualInterventionQueue: (studentGroup.individualInterventionQueue || []).filter(sId => sId !== studentId),
      lastModified: timestampInfo
    }
  });
  return {
    passed: true,
    nextSkill: nextBenchmarkSkill,
    nextAssessmentResultId: newAssRes._id
  };
}

const assignFirstNode = async (assessmentResult, studentGroup, studentId, currentRule) => {
  ninjalog.log({
    msg: "currentRule",
    val: currentRule,
    jsonString: true,
    context: "individual"
  });

  const nextBenchmarkAssessment = await Assessments.findOneAsync(currentRule.attributeValues.assessmentId);
  // Check for Errors
  if (currentRule && !nextBenchmarkAssessment) {
    throw Error(`Could not find next benchmark assessment for rule: ${currentRule._id}`);
    // Everything looks good, continue with assignment
  }
  const nextBenchmarkSkill = {
    assessmentId: nextBenchmarkAssessment._id,
    assessmentName: nextBenchmarkAssessment.name,
    interventions: []
  };
  // need to create a new assessment result

  const scoreTargets = assessmentsMethods.getScoreTargets({
    assessment: nextBenchmarkAssessment,
    grade: assessmentResult.grade,
    benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
    assessmentType: "individual"
  });

  const newAssRes = await createNewIndividualInterventionAssessmentResultForStudent({
    previousAssessmentResult: assessmentResult,
    studentId,
    skills: {
      benchmarkAssessmentId: nextBenchmarkAssessment._id,
      benchmarkAssessmentName: nextBenchmarkAssessment.name,
      benchmarkAssessmentTargets: scoreTargets,
      assessmentId: nextBenchmarkAssessment._id,
      assessmentName: nextBenchmarkAssessment.name,
      assessmentTargets: scoreTargets,
      interventions: nextBenchmarkAssessment.interventions
    },
    rootRuleId: currentRule._id
  });
  const lastModified = await getTimestampInfo(getMeteorUserId(), studentGroup.orgid, "assignFirstNode");
  await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
    $set: {
      individualInterventionQueue: (studentGroup.individualInterventionQueue || []).filter(sId => sId !== studentId),
      lastModified
    }
  });

  return {
    passed: true,
    nextSkill: nextBenchmarkSkill,
    nextAssessmentResultId: newAssRes._id
  };
};

export function findStudentsFirstFailingMeasureAndResultFromAssessmentResult(
  assessmentResult,
  studentId,
  allowNoScore
) {
  check(assessmentResult, Object);
  check(studentId, String);
  check(allowNoScore, Boolean);
  // find out if this student is present in any of the measure results for this assessment result
  const studentPresent = assessmentResult.measures.some(m =>
    m.studentResults.map(sr => sr.studentId).includes(studentId)
  );
  if (!studentPresent && !allowNoScore) {
    throw new Meteor.Error(403, `Student not present in any measures. studentId: ${studentId}`);
  } else if (!studentPresent) {
    return { targetMeasure: null, targetResult: null, didFailBenchmark: true };
  }
  return assessmentResult.measures.reduce(
    (a, c, measureIndex) => {
      const accumulatedFailureInfo = a;
      if (a.targetMeasure === null && !accumulatedFailureInfo.didFailBenchmark) {
        const studentResult = c.studentResults.find(sr => sr.studentId === studentId && !sr.meetsTarget);
        if (studentResult) {
          if (assessmentResult.type === "individual") {
            accumulatedFailureInfo.didFailBenchmark = measureIndex < 1;
          }
          if (!accumulatedFailureInfo.didFailBenchmark) {
            accumulatedFailureInfo.targetMeasure = c;
            accumulatedFailureInfo.targetResult = studentResult;
          }
        }
      }
      return accumulatedFailureInfo;
    },
    { targetMeasure: null, targetResult: null, didFailBenchmark: false }
  );
}

export async function processIndividualRule({ assessmentResult, studentId, allowNoScore, isResuming }) {
  check(assessmentResult, Object);
  check(studentId, String);
  check(allowNoScore, Boolean);

  const studentGroup = await StudentGroups.findOneAsync(assessmentResult.studentGroupId);
  let wasStudentRecommendedFromFourWeekRule = false;

  if (assessmentResult.grade === "HS") {
    wasStudentRecommendedFromFourWeekRule = studentGroup.individualInterventionQueue?.includes(studentId);
    if (wasStudentRecommendedFromFourWeekRule) {
      // eslint-disable-next-line no-param-reassign
      assessmentResult = await getStartingAssessmentResult({
        studentId,
        grade: assessmentResult.grade,
        schoolYear: assessmentResult.schoolYear,
        studentGroupId: assessmentResult.studentGroupId,
        orgid: assessmentResult.orgid,
        siteId: studentGroup.siteId,
        isFromFourWeekRule: true
      });
    }
  }

  const currentlyAssignedAssessmentResult = await AssessmentResults.findOneAsync({
    previousAssessmentResultId: assessmentResult._id,
    studentId,
    grade: assessmentResult.grade
  });
  const student = await Students.findOneAsync(studentId, { fields: { currentSkill: 1, history: 1 } });
  const studentOldestInterventionTimestamp = student.history?.[student.history?.length - 1]?.whenStarted?.on;
  const customDate = get(await getMeteorUser(), "profile.customDate");
  const currentDate = (await getCurrentDate(customDate, assessmentResult.orgid)).getTime();
  const oldestAvailableClasswideTimestamp = Math.min(
    ...[
      studentGroup.currentClasswideSkill?.whenStarted?.on,
      findLast(studentGroup.history, el => el.type === "classwide")?.whenStarted?.on,
      currentDate
    ].filter(f => f)
  );
  const shouldHSStudentContinue = !!(
    assessmentResult.grade === "HS" &&
    studentOldestInterventionTimestamp &&
    studentOldestInterventionTimestamp < oldestAvailableClasswideTimestamp
  );
  let isInDifferentProgressMonitoringTree = false;
  if (
    assessmentResult.grade === "HS" &&
    currentlyAssignedAssessmentResult?.status === "COMPLETED" &&
    !currentlyAssignedAssessmentResult.assessmentIds.includes(studentGroup.currentClasswideSkill.assessmentId)
  ) {
    isInDifferentProgressMonitoringTree = true;
  }

  const screeningAssignment = await ScreeningAssignments.findOneAsync({
    grade: assessmentResult.grade,
    benchmarkPeriodId: assessmentResult.benchmarkPeriodId
  });
  if (
    student?.currentSkill &&
    !student.currentSkill.assessmentId &&
    student?.history[0]?.assessmentId ===
      screeningAssignment.assessmentIds[screeningAssignment.assessmentIds.length - 1]
  ) {
    const benchmarkNameById = { "8S52Gz5o85hRkECgq": "Fall", nEsbWokBWutTZFkTh: "Winter", cjCMnZKARBJmG8suT: "Spring" };
    const benchmarkPeriodMessage =
      assessmentResult.grade !== "HS" ? ` for ${benchmarkNameById[assessmentResult.benchmarkPeriodId]}` : "";
    throw new Meteor.Error(
      403,
      `Student already completed all individual intervention skill trees${benchmarkPeriodMessage}.`
    );
  }

  if (
    student.history?.find(h => h.benchmarkAssessmentId === studentGroup.currentClasswideSkill?.assessmentId) &&
    assessmentResult.grade === "HS" &&
    Object.keys(student.currentSkill || {}).length &&
    !student.currentSkill.assessmentId
  ) {
    throw new Meteor.Error(403, "Already completed this progress monitoring tree", {
      studentId,
      skillName: studentGroup.currentClasswideSkill.assessmentName || assessmentResult.measures[0].assessmentName
    });
  }

  if (
    (currentlyAssignedAssessmentResult && !isInDifferentProgressMonitoringTree) ||
    (!student?.currentSkill &&
      assessmentResult.assessmentIds.includes(student?.history?.[0]?.assessmentId) &&
      !isResuming)
  ) {
    ninjalog.log({
      msg: "Individual Rule not processed. Student already has an assessment result shell from this assessment",
      context: "individual"
    });
    throw new Meteor.Error(
      403,
      "Individual rule not processed. Student already has an assessment result document created from this assessment result"
    );
  }

  // find first failure
  const {
    targetMeasure,
    targetResult,
    didFailBenchmark
  } = findStudentsFirstFailingMeasureAndResultFromAssessmentResult(assessmentResult, studentId, allowNoScore);
  ninjalog.log({
    msg: `didFailBenchmark = ${didFailBenchmark}`
  });

  const isTakingOnlyBenchmarkInDrillDownModeResult = await isTakingOnlyBenchmarkInDrillDownMode(
    assessmentResult.grade,
    translateBenchmarkPeriod(assessmentResult.benchmarkPeriodId).label,
    assessmentResult.measures[0].assessmentId,
    assessmentResult.rootRuleId
  );

  if (
    (assessmentResult.type === "individual" &&
      !didFailBenchmark &&
      (assessmentResult.measures.length > 1 || isTakingOnlyBenchmarkInDrillDownModeResult)) ||
    (assessmentResult.grade === "HS" && wasStudentRecommendedFromFourWeekRule && !student.currentSkill?.assessmentId)
  ) {
    // student is finished with this tree, assign next tree if available
    return assignNextTree(assessmentResult, studentGroup, studentId, shouldHSStudentContinue);
  }
  if (!targetMeasure && assessmentResult.type !== "individual") {
    const availableRules = Rules.find({
      "attributeValues.grade": assessmentResult.grade,
      "attributeValues.benchmarkPeriod": translateBenchmarkPeriod(assessmentResult.benchmarkPeriodId).label,
      "attributeValues.assessmentId": assessmentResult.measures[0].assessmentId
    }).fetchAsync();
    // SPRIN-1076 handle situations where there is more than one valid rule found.
    const currentRule = availableRules.find(rule => rule._id === rule.rootRuleId) || availableRules[0];
    const assessmentResultToPass = {
      ...assessmentResult,
      rootRuleId: currentRule.rootRuleId
    };
    return assignFirstNode(assessmentResultToPass, studentGroup, studentId, currentRule);
  }
  return assignNextNode({
    failedMeasure: targetMeasure,
    failedResult: targetResult,
    assessmentResult,
    studentGroup,
    studentId,
    isResuming
  });
}

export async function endCurrentIndividualIntervention({ studentId, studentGroup = null }) {
  const student = (await Students.findOneAsync(studentId, { fields: { currentSkill: 1, schoolYear: 1 } })) || {};
  const lastModified = await getTimestampInfo(getMeteorUserId(), student.orgid, "endCurrentIndividualIntervention");
  const query = {
    studentId,
    type: "individual",
    status: "OPEN",
    schoolYear: student.schoolYear
  };
  const openIndividualInterventions = await AssessmentResults.find(query).fetchAsync();

  if (openIndividualInterventions.length) {
    await AssessmentResults.removeAsync(query);
  }
  if (student.currentSkill?.assessmentResultId && student.currentSkill?.benchmarkAssessmentId) {
    await Students.updateAsync(studentId, { $unset: { currentSkill: 1 }, $set: { lastModified } });
  }
  if (studentGroup) {
    const studentGroupCurrentAssessmentResultIds =
      (await StudentGroups.findOneAsync({ _id: studentGroup._id }, { fields: { currentAssessmentResultIds: 1 } }))
        ?.currentAssessmentResultIds || [];
    const openIndividualInterventionIds = openIndividualInterventions.map(o => o._id);
    const currentAssessmentResultIds = studentGroupCurrentAssessmentResultIds.filter(
      s => !openIndividualInterventionIds.includes(s)
    );
    await StudentGroups.updateAsync(studentGroup._id, {
      $set: { currentAssessmentResultIds, lastModified }
    });
  }
  return true;
}

export async function removeIndividualInterventionProgress({
  studentId,
  benchmarkPeriodId,
  studentGroupId,
  schoolYear
}) {
  const currentAssessmentResultId = await Students.findOneAsync(
    { _id: studentId },
    { fields: { "currentSkill.assessmentResultId": 1 } }
  )?.currentSkill?.assessmentResultId;
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "removeIndividualInterventionProgress");
  await AssessmentResults.removeAsync({
    studentId,
    studentGroupId,
    benchmarkPeriodId,
    schoolYear,
    type: "individual"
  });
  await StudentGroups.updateAsync(
    { _id: studentGroupId },
    { $pull: { currentAssessmentResultIds: currentAssessmentResultId }, $set: { lastModified } }
  );
  await Students.updateAsync(studentId, {
    $pull: { history: { benchmarkPeriodId } },
    $unset: { currentSkill: 1 },
    $set: { lastModified }
  });
  return true;
}

export async function getStartingAssessmentResult({
  studentId,
  grade,
  schoolYear,
  studentGroupId,
  orgid,
  siteId,
  isFromFourWeekRule = false
}) {
  const studentGroup =
    (await StudentGroups.findOneAsync(
      { _id: studentGroupId },
      {
        fields: {
          grade: 1,
          currentClasswideSkill: 1,
          history: 1
        }
      }
    )) || {};
  const student = await Students.findOneAsync(studentId);
  let benchmarkPeriodId;
  if (isHighSchoolGrade(grade)) {
    benchmarkPeriodId = "allPeriods";
  } else {
    const benchmarkPeriod = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
    benchmarkPeriodId = benchmarkPeriod._id;
  }
  const screeningAssignment = await ScreeningAssignments.findOneAsync({ grade, benchmarkPeriodId });
  let assessmentId;
  const { currentSkill, history } = student || {};

  if (studentGroup.grade === "HS") {
    const hasGroupCompletedClasswide =
      studentGroup.currentClasswideSkill && !studentGroup.currentClasswideSkill?.assessmentId;

    if (isFromFourWeekRule) {
      // NOTE(fmazur) - use previous assessment when scheduling for individual intervention in HS
      assessmentId = studentGroup.history
        .filter(h => h.type === "classwide")
        .find(h => h.assessmentId !== studentGroup.currentClasswideSkill.assessmentId)?.assessmentId;
      if (!currentSkill?.assessmentId && history?.find(h => h.benchmarkAssessmentId === assessmentId)) {
        // NOTE(fmazur) - if student completed four week recommended skill then use current
        assessmentId = studentGroup.currentClasswideSkill.assessmentId;
      }
    } else if (!currentSkill?.assessmentId && history?.[0]?.assessmentId) {
      assessmentId = history[0].assessmentId;
      const ar = await AssessmentResults.findOneAsync({ _id: history[0].assessmentResultId });
      if (ar) {
        return { _id: "startingAssessmentResult", ...ar };
      }
    } else if (hasGroupCompletedClasswide) {
      const passedClasswideAssessmentResults = sortBy(
        await AssessmentResults.find({
          studentGroupId,
          status: "COMPLETED",
          type: "classwide",
          "ruleResults.passed": true
        }).fetchAsync(),
        "created.on"
      );
      const frustrationalAssessmentResult = passedClasswideAssessmentResults.find(ar => {
        const [frustrational] = ar.measures[0].targetScores;
        return ar.measures[0].studentResults.find(sr => sr.studentId === studentId)?.score < frustrational;
      });
      assessmentId = frustrationalAssessmentResult
        ? frustrationalAssessmentResult.measures[0].assessmentId
        : studentGroup.history[0].assessmentId;
    } else if (studentGroup?.currentClasswideSkill?.assessmentId) {
      assessmentId = studentGroup.currentClasswideSkill.assessmentId;
    } else {
      [assessmentId] = screeningAssignment.assessmentIds;
    }
  } else {
    [assessmentId] = screeningAssignment.assessmentIds;
  }
  const assessment = await Assessments.findOneAsync(assessmentId);
  if (!assessment) {
    throw new Meteor.Error(500, "There is no available assessment.");
  }
  const targetsForGrade = assessment.strands[0].scores[0].targets.filter(target => target.grade === grade);
  const target =
    targetsForGrade.find(t => t.assessmentType === "individual") ||
    targetsForGrade.find(t => !t.assessmentType) ||
    targetsForGrade[0];

  if (!target) {
    throw new Meteor.Error(500, "There are no targets defined for this assessment.");
  }

  const targetResult = {
    studentId,
    status: "COMPLETE",
    firstName: student.identity.name.firstName,
    lastName: student.identity.name.lastName,
    score: "0",
    meetsTarget: false,
    individualRuleOutcome: "below"
  };
  const targetMeasure = {
    assessmentId,
    assessmentName: assessment.name,
    cutoffTarget: target.periods[0].values[0],
    targetScores: target.periods[0].values,
    medianScore: 0,
    studentScores: [0],
    percentMeetingTarget: 0,
    numberMeetingTarget: 0,
    totalStudentsAssessed: 1,
    studentResults: [targetResult]
  };
  return {
    _id: "startingAssessmentResult",
    benchmarkPeriodId,
    schoolYear,
    status: "COMPLETED",
    studentGroupId,
    type: "benchmark",
    grade,
    orgid,
    studentId,
    assessmentIds: screeningAssignment.assessmentIds,
    scores: [
      {
        ...targetResult,
        value: targetResult.score,
        siteId,
        orgid,
        assessmentId
      }
    ],
    measures: [targetMeasure]
  };
}

if (Meteor.isServer) {
  Meteor.methods({
    async processIndividualRule({ assessmentResultId, studentId, allowNoScore }) {
      check(assessmentResultId, Match.Maybe(String));
      check(studentId, String);
      check(allowNoScore, Boolean);
      let assessmentResult = assessmentResultId ? await AssessmentResults.findOneAsync(assessmentResultId) : null;
      const schoolYear = await getCurrentSchoolYear(await getMeteorUser());
      const { siteId, studentGroupId, grade, orgid } = await StudentGroupEnrollments.findOneAsync({
        studentId,
        isActive: true,
        schoolYear
      });
      if (!(await hasSiteAccess(this.userId, siteId))) {
        throw new Meteor.Error(403, "You don’t have permissions to do this.");
      }
      if (!assessmentResultId) {
        assessmentResult = getStartingAssessmentResult({
          studentId,
          grade,
          schoolYear,
          studentGroupId,
          orgid,
          siteId
        });
      }
      return processIndividualRule({
        assessmentResult,
        studentId,
        allowNoScore
      });
    },
    async endCurrentIndividualIntervention({ studentId, studentGroupId }) {
      check(studentId, String);
      check(studentGroupId, String);
      const studentGroup = await StudentGroups.findOneAsync(
        { _id: studentGroupId },
        {
          fields: {
            schoolYear: 1,
            siteId: 1,
            currentAssessmentResultIds: 1,
            individualInterventionQueue: 1
          }
        }
      );
      if (
        await auth.hasAccess(["admin", "universalCoach"], {
          userId: this.userId,
          siteId: studentGroup.siteId
        })
      ) {
        return endCurrentIndividualIntervention({ studentId, studentGroup });
      }
      throw new Meteor.Error("403", "You are not authorized to end the student's intervention");
    },
    async removeIndividualInterventionProgressAndStartOver({ assessmentResultId, studentId, siteId, studentGroupId }) {
      check(assessmentResultId, Match.Maybe(String));
      check(studentId, String);
      check(siteId, String);
      check(studentGroupId, String);
      if (
        await auth.hasAccess(["admin", "universalCoach"], {
          userId: this.userId,
          siteId
        })
      ) {
        let benchmarkPeriodId;
        const { schoolYear } = await Students.findOneAsync(studentId);
        const { grade, orgid } = await StudentGroupEnrollments.findOneAsync({
          studentId,
          isActive: true,
          schoolYear
        });
        if (isHighSchoolGrade(grade)) {
          benchmarkPeriodId = "allPeriods";
          const resultOfRemoving = removeIndividualInterventionProgress({
            studentId,
            benchmarkPeriodId,
            studentGroupId,
            schoolYear
          });
          await removeStudentFromAllSkillGroupsForSite({ siteId, studentId });
          return resultOfRemoving;
        }

        const assessmentResult =
          (await AssessmentResults.findOneAsync(assessmentResultId)) ||
          getStartingAssessmentResult({
            studentId,
            grade,
            schoolYear,
            studentGroupId,
            orgid,
            siteId
          });
        ({ benchmarkPeriodId } = assessmentResult);

        removeIndividualInterventionProgress({
          studentId,
          benchmarkPeriodId,
          studentGroupId,
          schoolYear
        });
        await removeStudentFromAllSkillGroupsForSite({ siteId, studentId });
        return processIndividualRule({
          assessmentResult,
          studentId,
          allowNoScore: true
        });
      }
      throw new Meteor.Error("403", "You are not authorized to remove the student's intervention progress");
    }
  });
}

export default processIndividualRule;
