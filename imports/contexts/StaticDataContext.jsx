import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { UserContext } from "./UserContext";

const getDefaultContext = () => {
  return {
    rules: [],
    roleDefinitions: [],
    news: [],
    grades: [],
    instructionalVideos: [],
    settings: [],
    interventions: [],
    groupedAssessments: [],
    benchmarkPeriods: [],
    env: {},
    isDevMode: false
  };
};

export const StaticDataContext = createContext(getDefaultContext());

export const StaticDataContextProvider = ({ children }) => {
  const { userId } = useContext(UserContext);
  const [data, setData] = useState(getDefaultContext());

  useEffect(() => {
    if (userId) {
      Meteor.call("StaticData:getStaticData", (err, result) => {
        if (!err && result) {
          setData(result);
        }
      });
    } else {
      setData(getDefaultContext());
    }
  }, [userId]);

  return <StaticDataContext.Provider value={data}>{children}</StaticDataContext.Provider>;
};

StaticDataContextProvider.propTypes = {
  children: PropTypes.node
};
