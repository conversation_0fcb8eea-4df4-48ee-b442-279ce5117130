import React, { createContext, useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import { UserContext } from "./UserContext";
import { OrganizationContext } from "./OrganizationContext";
import { getLatestAvailableSchoolYear } from "../api/utilities/utilities";

export const SchoolYearContext = createContext({
  schoolYear: null,
  latestAvailableSchoolYear: null,
  customDate: null,
  setSchoolYear: () => {},
  setLatestAvailableSchoolYear: () => {},
  setCustomDate: () => {}
});

export const SchoolYearContextProvider = ({ children }) => {
  const { user } = useContext(UserContext);
  const { orgId } = useContext(OrganizationContext);

  const [schoolYear, setSchoolYear] = useState(null);
  const [latestAvailableSchoolYear, setLatestAvailableSchoolYear] = useState(null);
  const [customDate, setCustomDate] = useState(null);

  useEffect(() => {
    if (!user) {
      return;
    }

    const init = async () => {
      const latest = await getLatestAvailableSchoolYear(user, orgId);
      setLatestAvailableSchoolYear(latest);

      let resolvedSchoolYear = latest;

      if (!user?.profile?.customDate?.length && user?.profile?.selectedSchoolYear) {
        resolvedSchoolYear = user.profile.selectedSchoolYear;
      }

      setSchoolYear(resolvedSchoolYear);

      const custom = user?.profile?.customDate ?? null;
      setCustomDate(custom);
    };

    // eslint-disable-next-line no-console
    init().catch(console.error);
  }, [user, orgId]);

  return (
    <SchoolYearContext.Provider
      value={{
        schoolYear,
        setSchoolYear,
        latestAvailableSchoolYear,
        setLatestAvailableSchoolYear,
        customDate,
        setCustomDate
      }}
    >
      {children}
    </SchoolYearContext.Provider>
  );
};

SchoolYearContextProvider.propTypes = {
  children: PropTypes.node
};
