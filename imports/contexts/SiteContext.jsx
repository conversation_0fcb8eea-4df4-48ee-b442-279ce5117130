import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { useParams } from "react-router-dom";
import { isEqual, get } from "lodash";
import { OrganizationContext } from "./OrganizationContext";
import { UserContext } from "./UserContext";
import { SchoolYearContext } from "./SchoolYearContext";
import { StudentGroups } from "../api/studentGroups/studentGroups";
import { BenchmarkWindows } from "../api/benchmarkWindows/benchmarkWindows";
import { getCurrentDate } from "../api/helpers/getCurrentDate";

export const SiteContext = createContext({
  siteId: null,
  siteName: "",
  site: null,
  studentGroupsInSite: [],
  userStudentGroups: [],
  currentBenchmarkWindow: {},
  isLoading: false
});

export const SiteContextProvider = ({ children, siteId }) => {
  const { currentSiteAccess, userId, user } = useContext(UserContext);
  const { sitesInOrg, orgId } = useContext(OrganizationContext);
  const { schoolYear } = useContext(SchoolYearContext);
  const { studentGroupId: routeStudentGroupId } = useParams();

  const [potentialSite, setPotentialSite] = useState(null);
  const [studentGroupsInSite, setStudentGroupsInSite] = useState([]);
  const [currentBenchmarkWindow, setCurrentBenchmarkWindow] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  const userStudentGroups = useTracker(() => {
    if (!userId || !schoolYear || !siteId || !orgId) {
      return [];
    }

    // TODO(fmazur) - replace this with use effect that filters based on user orgid, user siteIds or primary role? and schoolYear
    const subscription = Meteor.subscribe("StudentGroupsAssociatedWithUser", schoolYear, siteId, orgId);
    if (subscription.ready()) {
      return StudentGroups.find({}, { sort: { name: 1 } }).fetch();
    }
    return [];
    // TODO(fmazur) - maybe remove dependency list to auto update if needed?
  }, [userId, schoolYear, siteId, orgId]);

  const currentStudentGroupData = useTracker(() => {
    if (!routeStudentGroupId) {
      return null;
    }

    const subscription = Meteor.subscribe("StudentGroupById", routeStudentGroupId);
    if (subscription.ready()) {
      return StudentGroups.findOne(routeStudentGroupId);
    }
    return null;
  });

  const benchmarkWindowsData = useTracker(() => {
    if (!siteId) {
      return [];
    }

    const subscription = Meteor.subscribe("BenchmarkWindowsForSchool", siteId);
    if (subscription.ready()) {
      return BenchmarkWindows.find({ siteId }).fetch();
    }
    return [];
  }, [siteId]);

  useEffect(() => {
    if (sitesInOrg.length) {
      // TODO(fmazur) - use current schoolYear siteId of a user if available otherwise use context siteId
      const site = sitesInOrg.find(s => s._id === (siteId || currentSiteAccess?.siteId));
      setPotentialSite(site);
      setIsLoading(false);
    }
  }, [sitesInOrg, siteId]);

  useEffect(() => {
    // TODO(fmazur) - limit data maybe for usage for all roles
    // TODO(fmazur) - StudentGroupsAssociatedWithUser publication
    if (orgId && siteId && schoolYear) {
      Meteor.call("StudentGroups:getGroups", { orgId, siteId, schoolYear }, (err, result) => {
        if (!err) {
          setStudentGroupsInSite(result);
        }
      });
    }
  }, [orgId, siteId, schoolYear]);

  useEffect(() => {
    if (!userId) {
      setPotentialSite(null);
      setStudentGroupsInSite([]);
    }
  }, [userId]);

  useEffect(() => {
    if (user && orgId && siteId && schoolYear && benchmarkWindowsData.length) {
      (async () => {
        const { customDate } = get(user, "profile", {});
        const currentDate = await getCurrentDate(customDate, orgId);
        const benchmarkWindow = benchmarkWindowsData.find(
          bw =>
            bw.schoolYear === schoolYear &&
            bw.siteId === siteId &&
            bw.startDate <= currentDate &&
            bw.endDate >= currentDate
        );
        setCurrentBenchmarkWindow(benchmarkWindow);
      })();
    }
  }, [user, orgId, benchmarkWindowsData, schoolYear, siteId]);

  useEffect(() => {
    if (currentStudentGroupData && routeStudentGroupId) {
      const updatedGroups = [...studentGroupsInSite];
      const index = updatedGroups.findIndex(group => group._id === routeStudentGroupId);
      if (!isEqual(updatedGroups[index], currentStudentGroupData)) {
        if (index >= 0) {
          updatedGroups[index] = currentStudentGroupData;
        } else {
          updatedGroups.push(currentStudentGroupData);
        }
        setStudentGroupsInSite(updatedGroups);
      }
    }
  }, [currentStudentGroupData, routeStudentGroupId]);

  return (
    <SiteContext.Provider
      value={{
        siteId: userId ? siteId || potentialSite?._id || null : null,
        siteName: potentialSite?.name || "",
        site: potentialSite || null,
        studentGroupsInSite: studentGroupsInSite || [],
        userStudentGroups: userStudentGroups || [],
        currentBenchmarkWindow: currentBenchmarkWindow || {},
        isLoading
      }}
    >
      {children}
    </SiteContext.Provider>
  );
};

SiteContextProvider.propTypes = {
  children: PropTypes.node,
  siteId: PropTypes.string
};
