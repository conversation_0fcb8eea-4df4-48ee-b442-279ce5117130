import { ORGID, TEST_GROUPS } from "../support/common/constants";
import replaceExternalWindow from "../support/common/printWindowUtil";
import { getSchoolYearLabel } from "../support/common/utils";
import { clickDashboardTab, clickElementByTextWithinSideNav } from "../support/common/navigating";

const printStubOpts = {
  onBeforeLoad: win => {
    cy.stub(win, "print");
  }
};

/**
 * Groups used: grade2group1, grade3group1
 * Modifies: -
 * Can be rerun without test database restart
 * Requires groups with screening, individual and classwide interventions
 */
describe("Screening page printing:", () => {
  describe("Teacher prints the screening page", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
    });
    it("and sees the print page preview with needed elements", () => {
      goToScreening(TEST_GROUPS.grade2group1.name);
      printScreeningPage();

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          cy.findByText(`${getSchoolYearLabel()} Screening Results`, { exact: false }).should("exist");
          cy.findByText(TEST_GROUPS.grade2group1.name).should("exist");
          cy.get(".screening-results").should("exist");
          cy.get(".screening-page-graph").should("exist");
        },
        { timeout: 20000 }
      );
    });
    it("and sees all assessment measure graphs for the classwide overview", () => {
      goToScreening(TEST_GROUPS.grade2group1.name);
      printScreeningPage();

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          cy.findByText("40% of your class reached the target on all of the screening assessments.").should("exist");
          cy.get(".screening-page-graph").should("have.length", 4);
          verifyGraphNameElements(4, "classwide-screening-graph");
          cy.get("#classwide-screening-summary").should("exist");
        },
        { timeout: 20000 }
      );
    });
    it("and sees all assessment measure graphs for individual recommendation", () => {
      goToScreening(TEST_GROUPS.grade2group1.name);
      printScreeningPage();

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          cy.findByText("40% of your class reached the target on all of the screening assessments", {
            exact: false
          }).should("exist");
          cy.get(".screening-page-graph").should("have.length", 4);
          verifyGraphNameElements(3, "classwide-screening-graph");
          cy.get("#individual-screening-summary").should("not.exist");
        },
        { timeout: 20000 }
      );
    });
  });

  describe("Admin prints the screening page", () => {
    it("and sees the print page preview", () => {
      cy.loginAs({ role: "coach" });
      clickElementByTextWithinSideNav("2nd Grade");
      cy.findByTestId("screeningNotice")
        .should("be.visible")
        .within(() => {
          cy.contains(TEST_GROUPS.grade2group1.name)
            .should("be.visible")
            .click();
        });
      clickDashboardTab(TEST_GROUPS.grade2group1.name, "screeningTab");
      cy.findAllByTestId("viewScreening")
        .first()
        .click();
      printScreeningPage();

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          cy.findByText(`${getSchoolYearLabel()} Screening Results`, { exact: false }).should("exist");
          cy.findByText(TEST_GROUPS.grade2group1.name).should("exist");
          cy.get(".screening-results").should("exist");
          cy.get(".screening-page-graph").should("exist");
          cy.get("#classwide-screening-summary").should("exist");
        },
        { timeout: 20000 }
      );
    });
  });
});

function verifyGraphNameElements(numberOfMeasures, graphType) {
  for (let i = 0; i < numberOfMeasures; i += 1) {
    cy.findByTestId(`${graphType}_${i}`).within(() => {
      cy.findByTestId(`measure-results-detail-name_${i}`).should("exist");
    });
  }
}

function printScreeningPage() {
  cy.findByText("Classroom Performance").should("be.visible");
  cy.findByText("Print This Page").click();
}

function goToScreening(groupName) {
  cy.contains(groupName).click();
  clickDashboardTab(groupName, "screeningTab");
  cy.findAllByTestId("viewScreening")
    .first()
    .click();
}

function makeWindowRelatedAssertions({ url }) {
  const [siteId, , studentGroupId, , , assessmentResultId] = url.replace("http://localhost:3000/site/", "").split("/");
  const targetUrl = `http://localhost:3000/${ORGID}/print/Screening?siteId=${siteId}&studentGroupId=${studentGroupId}&assessmentResultId=${assessmentResultId}&orgid=${ORGID}`;
  cy.visit(targetUrl, printStubOpts);
  replaceExternalWindow(targetUrl);
}
