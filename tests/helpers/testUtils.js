import React from "react";
import { render, act } from "@testing-library/react"; // eslint-disable-line
import { Router } from "react-router-dom";
import { createMemoryHistory } from "history";
import { AppDataProvider } from "../../imports/ui/routing/AppDataContext";
import { user } from "../../imports/test-helpers/data/users";
import { OrganizationContext } from "../../imports/contexts/OrganizationContext";
import { SchoolYearContext } from "../../imports/contexts/SchoolYearContext";
import { UserContext } from "../../imports/contexts/UserContext";
import { SiteContext } from "../../imports/contexts/SiteContext";
import { StudentGroupContext } from "../../imports/contexts/StudentGroupContext";
import { StaticDataContext } from "../../imports/contexts/StaticDataContext";
import { ROLE_IDS } from "../cypress/support/common/constants";

function getHistory(paths = ["/"]) {
  return createMemoryHistory({ initialEntries: paths });
}

function getMatch({ params = {}, isExact = true } = {}) {
  return { params, isExact };
}

function getLocation({ pathname, queryString } = {}) {
  return {
    pathname,
    search: queryString
  };
}

function getRouterProps({ paths, params, isExact, pathname, queryString } = {}) {
  return {
    history: getHistory(paths),
    match: getMatch({ params, isExact }),
    location: getLocation({ pathname, queryString })
  };
}

// eslint-disable-next-line react/prop-types
const UserContextProvider = ({ children, value: override = {} }) => {
  const defaultValue = {
    userId: "test_user_id",
    userOrgId: "test_organization_id",
    userRoles: [ROLE_IDS.teacher],
    currentSiteAccess: { siteId: "test_site_id", role: ROLE_IDS.teacher },
    firstName: "Test",
    lastName: "User",
    warn: false
  };

  return <UserContext.Provider value={{ ...defaultValue, ...override }}>{children}</UserContext.Provider>;
};

// eslint-disable-next-line react/prop-types
const OrganizationContextProvider = ({ children, value: override = {} }) => {
  const defaultValue = {
    orgId: "test_organization_id",
    isSelfEnrollee: false,
    sitesInOrg: [
      {
        _id: "test_site_id",
        name: "Test Site",
        orgid: "test_organization_id",
        grades: ["01", "02", "03"]
      }
    ]
  };
  return (
    <OrganizationContext.Provider value={{ ...defaultValue, ...override }}>{children}</OrganizationContext.Provider>
  );
};

// eslint-disable-next-line react/prop-types
const SchoolYearContextProvider = ({ children, value: override = {} }) => {
  const defaultValue = {
    schoolYear: 2019,
    latestAvailableSchoolYear: 2019
  };
  return <SchoolYearContext.Provider value={{ ...defaultValue, ...override }}>{children}</SchoolYearContext.Provider>;
};

// eslint-disable-next-line react/prop-types
const SiteContextProvider = ({ children, value: override = {} }) => {
  const defaultValue = {
    siteId: "test_site_id",
    siteName: "Test Site",
    site: {
      _id: "test_site_id",
      name: "Test Site",
      orgid: "test_organization_id",
      grades: ["01", "02", "03"]
    }
  };
  return <SiteContext.Provider value={{ ...defaultValue, ...override }}>{children}</SiteContext.Provider>;
};

// eslint-disable-next-line react/prop-types
const StudentGroupContextProvider = ({ children, value: override = {} }) => {
  const defaultValue = {
    studentGroup: {
      grade: "01"
    }
  };
  return (
    <StudentGroupContext.Provider value={{ ...defaultValue, ...override }}>{children}</StudentGroupContext.Provider>
  );
};

// eslint-disable-next-line react/prop-types
const StaticDataContextProvider = ({ children, value: override = {} }) => {
  const defaultValue = {
    rules: [],
    roleDefinitions: [
      { _id: "arbitraryIdteacher", name: "teacher" },
      { _id: "arbitraryIdadmin", name: "admin" },
      { _id: "arbitraryIdsupport", name: "support" },
      { _id: "arbitraryIduniversalCoach", name: "universalCoach" }
    ],
    news: [],
    grades: [],
    instructionalVideos: [],
    settings: [],
    interventions: [],
    groupedAssessments: [],
    benchmarkPeriods: []
  };
  return <StaticDataContext.Provider value={{ ...defaultValue, ...override }}>{children}</StaticDataContext.Provider>;
};

function renderWithRouter(
  ui,
  {
    route = "/",
    history = createMemoryHistory({ initialEntries: [route] }),
    userContextValue = {},
    organizationContextValue = {},
    schoolYearContextValue = {},
    siteContextValue = {},
    studentGroupContextValue = {},
    staticDataContextValue = {},
    ...renderOptions // eslint-disable-line
  } = {}
) {
  const testUser = user("Test", "User", "teacher");
  const testEnv = { METEOR_ENVIRONMENT: "test" };
  const testSchoolYear = 2019;

  let renderResult;
  act(() => {
    renderResult = render(
      <UserContextProvider value={userContextValue}>
        <Router history={history}>
          <OrganizationContextProvider value={organizationContextValue}>
            <SchoolYearContextProvider value={schoolYearContextValue}>
              <SiteContextProvider value={siteContextValue}>
                <StudentGroupContextProvider value={studentGroupContextValue}>
                  <StaticDataContextProvider value={staticDataContextValue}>
                    <AppDataProvider
                      user={testUser}
                      env={testEnv}
                      schoolYear={testSchoolYear}
                      orgid="test_organization_id"
                    >
                      {ui}
                    </AppDataProvider>
                  </StaticDataContextProvider>
                </StudentGroupContextProvider>
              </SiteContextProvider>
            </SchoolYearContextProvider>
          </OrganizationContextProvider>
        </Router>
      </UserContextProvider>,
      renderOptions
    );
  });

  return {
    ...renderResult,
    history
  };
}

// re-export everything
export * from "@testing-library/react";

export { renderWithRouter, getRouterProps, getHistory };
